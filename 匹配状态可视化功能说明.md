# 匹配状态可视化功能说明

## 功能概述

新增的匹配状态可视化功能允许用户在轨迹接力可视化系统中切换输入目标的显示模式，通过颜色区分已匹配和未匹配的输入目标，帮助直观分析轨迹接力算法的性能。

## 核心功能

### 1. 匹配状态显示模式

**功能描述**：
- 通过按键M切换输入目标的颜色显示模式
- 启用时：根据目标的匹配状态显示不同颜色
- 禁用时：恢复基于目标ID的原有颜色逻辑

**颜色方案**：
- **绿色**：已匹配的输入目标（`is_matched=True`）
- **红色**：未匹配的输入目标（`is_matched=False`或无`is_matched`属性）

### 2. 区域特定显示

**左侧区域（输入目标）**：
- 受匹配状态显示模式影响
- 启用时显示匹配状态颜色
- 禁用时显示原有ID颜色

**右侧区域（输出目标）**：
- 不受匹配状态显示模式影响
- 始终保持原有的基于ID的颜色逻辑

### 3. 状态指示器

**位置**：左上角信息面板
**内容**：
- 匹配状态显示开关状态
- 颜色说明（启用时显示）

**显示示例**：
```
Matching Status: ON
Green=Matched, Red=Unmatched
```

## 使用方法

### 基本操作

1. **启动程序**：
   ```bash
   python 轨迹接力可视化.py
   ```

2. **切换匹配状态显示**：
   - 按键：`M` 或 `m`
   - 功能：在匹配状态显示和原有颜色显示之间切换

3. **查看状态**：
   - 左上角面板显示当前模式状态
   - 终端输出切换确认信息

### 键盘控制

更新后的完整键盘控制：
```
Space = 暂停/播放
A/D = 后退/前进
C = 车辆图像/圆点切换
I = ID显示模式切换
M = 匹配状态显示切换  ← 新增
T = 文本碰撞检测切换
B = 文本背景切换
+/- = 文本大小调整
P = 动态文本位置切换
ESC/Q = 退出
```

## 技术实现

### 1. 新增属性

**Visualizer类新增属性**：
```python
self.show_matching_status = False  # 匹配状态显示开关，默认关闭
```

### 2. 核心方法

**切换方法**：
```python
def toggle_matching_status_display(self):
    """切换匹配状态显示模式"""
    self.show_matching_status = not self.show_matching_status
    return "Enabled" if self.show_matching_status else "Disabled"
```

**颜色获取方法**：
```python
def _get_matching_status_color(self, point, region_key):
    """根据匹配状态和区域获取颜色"""
    if region_key == 'left' and self.show_matching_status:
        if hasattr(point, 'is_matched') and point.is_matched:
            return (0, 255, 0)  # 绿色 - 已匹配
        else:
            return (0, 0, 255)  # 红色 - 未匹配
    return self._get_color_by_id(point.get_id_str())  # 原有颜色
```

### 3. 界面更新

**信息面板增强**：
- 面板高度增加到100像素
- 添加匹配状态指示器
- 动态显示颜色说明

**目标绘制逻辑**：
- 修改`draw_targets`方法中的颜色获取逻辑
- 根据区域和模式状态选择合适的颜色方案

## 应用场景

### 1. 算法性能分析
- **快速识别**：一眼看出哪些输入目标成功参与了融合
- **问题定位**：红色目标表示可能的算法问题或数据质量问题
- **效果评估**：绿色目标比例反映算法的整体性能

### 2. 数据质量检查
- **孤立目标识别**：红色目标可能是孤立的、无法融合的目标
- **设备性能评估**：某个设备的目标大量显示为红色可能表示设备问题
- **时间段分析**：观察不同时间段的匹配率变化

### 3. 调试和优化
- **参数调优**：调整算法参数后观察颜色变化
- **实时反馈**：在可视化过程中实时了解匹配效果
- **对比分析**：在原有颜色和匹配状态颜色之间切换对比

## 使用示例

### 启用匹配状态显示
```
按键 M 后终端输出：
Matching status display: Enabled
  - Input targets: Green=Matched, Red=Unmatched
  - Output targets: Original colors
```

### 禁用匹配状态显示
```
按键 M 后终端输出：
Matching status display: Disabled
  - All targets: Original ID-based colors
```

## 注意事项

1. **数据依赖**：需要先运行目标匹配关系建立功能，确保输入目标具有`is_matched`属性
2. **区域限制**：只影响左侧输入目标显示，右侧输出目标保持不变
3. **默认状态**：程序启动时默认关闭匹配状态显示模式
4. **兼容性**：与现有的所有可视化功能完全兼容
5. **性能影响**：颜色计算逻辑简单，对性能影响微乎其微

## 与其他功能的协同

- **目标匹配关系功能**：提供`is_matched`属性数据源
- **统计分析功能**：可视化结果与统计数据相呼应
- **动态文本位置**：文本颜色随目标颜色变化
- **ID显示模式**：可与匹配状态显示同时使用

这个功能为轨迹接力可视化系统提供了强大的分析工具，使用户能够直观地理解和评估轨迹接力算法的性能表现。
