#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
目标ID文本重叠问题修复测试脚本

此脚本用于测试和演示轨迹接力可视化系统中新增的文本重叠避免功能。

功能特性:
1. 智能文本布局算法 - 自动寻找最优文本位置避免重叠
2. 动态文本大小调整 - 根据目标密度自动调整文本大小
3. 文本背景和边框 - 提高文本可读性
4. 碰撞检测系统 - 检测并避免文本重叠
5. 螺旋搜索算法 - 当常规位置都被占用时使用螺旋搜索
6. 实时参数调整 - 通过键盘快捷键实时调整显示参数

键盘控制:
- I: 切换ID显示模式（目标旁边 vs 右上角面板）
- T: 开启/关闭文本碰撞检测
- B: 开启/关闭文本背景
- +/-: 增加/减少文本大小
- R: 开启/关闭动态文本大小调整
- C: 切换车辆图像显示
- Space: 暂停/播放
- A/D: 后退/前进10帧
- ESC/Q: 退出

使用方法:
python test_text_overlap_fix.py [--log_file path] [--show_window True]
"""

import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from objs.Visualizer import Visualizer
from objs.LaneLine import LaneLine
from objs.TrackPoint import TrackPoint
from utils import lonlat_to_mercator
import numpy as np
import cv2


def create_test_data():
    """
    创建测试数据，模拟高密度目标场景

    返回:
        tuple: (lane_lines, track_frames_output, track_frames_input)
    """
    # 创建简单的车道线数据
    lane_lines = []
    lane1 = LaneLine(1, 10)
    # 创建一条直线车道线
    for i in range(10):
        lon = 112.989427 + i * 0.0001
        lat = 28.090128
        lane1.points.append((lon, lat))
    lane_lines.append(lane1)

    # 创建高密度目标数据来测试文本重叠
    track_frames_output = []
    track_frames_input = []

    # 生成50帧数据
    for frame_idx in range(50):
        output_points = []
        input_points = []

        # 在每帧中创建多个密集分布的目标
        target_count = 15 + (frame_idx % 10)  # 15-25个目标，模拟密度变化

        for i in range(target_count):
            # 在基准点周围创建密集分布的目标
            base_lon = 112.989427
            base_lat = 28.090128

            # 创建密集的目标分布
            offset_x = (i % 5 - 2) * 0.00005  # 5x5网格分布
            offset_y = (i // 5 - 2) * 0.00003

            # 添加一些随机偏移来模拟真实场景
            offset_x += (np.random.random() - 0.5) * 0.00002
            offset_y += (np.random.random() - 0.5) * 0.00002

            lon = base_lon + offset_x
            lat = base_lat + offset_y

            # 创建输出目标点
            output_point = TrackPoint(
                splice_id=f"OUT_{i}",
                target_id=1000 + i,
                device_id=1,
                timestamp_ms=frame_idx * 100,
                longitude=lon,
                latitude=lat,
                longitude_update=lon,
                latitude_update=lat,
                is_pass_ovd=1 if i % 3 == 0 else 0  # 部分目标标记为超限
            )
            output_points.append(output_point)

            # 创建输入目标点（稍微不同的位置）
            input_point = TrackPoint(
                splice_id=f"IN_{i}",
                target_id=2000 + i,
                device_id=2,
                timestamp_ms=frame_idx * 100,
                longitude=lon + 0.00001,
                latitude=lat + 0.00001,
                longitude_update=lon + 0.00001,
                latitude_update=lat + 0.00001,
                is_pass_ovd=0
            )
            input_points.append(input_point)

        track_frames_output.append(output_points)
        track_frames_input.append(input_points)

    return lane_lines, track_frames_output, track_frames_input


def main():
    """
    主测试函数
    """
    parser = argparse.ArgumentParser(description='目标ID文本重叠问题修复测试')
    parser.add_argument('--log_file', type=str, default=None, help='日志文件路径（可选，不提供则使用测试数据）')
    parser.add_argument('--show_window', type=bool, default=True, help='是否显示窗口')
    parser.add_argument('--scale_factor', type=float, default=3.0, help='缩放因子')
    parser.add_argument('--display_range', type=float, default=800.0, help='显示范围（米）')
    parser.add_argument('--dynamic_text_position', type=bool, default=True, help='是否启用动态文本位置功能')

    args = parser.parse_args()

    # 设置基准点
    base_point = (112.989427, 28.090128)
    base_point_mercator = lonlat_to_mercator(base_point[0], base_point[1])

    if args.log_file and os.path.exists(args.log_file):
        print("使用真实日志数据...")
        # 这里可以添加真实日志解析逻辑
        # 为了演示，我们仍然使用测试数据
        lane_lines, track_frames_output, track_frames_input = create_test_data()
    else:
        print("使用测试数据演示文本重叠修复功能...")
        lane_lines, track_frames_output, track_frames_input = create_test_data()

    # 设备列表
    device_list = [(112.992244, 28.089316), (112.98661, 28.090939)]

    # 创建可视化器，使用较大的文本大小来更好地演示重叠问题
    visualizer = Visualizer(
        base_point=base_point,
        base_point_mercator=base_point_mercator,
        lane_line_list=lane_lines,
        track_output_point_frame_list=track_frames_output,
        track_input_point_frame_list=track_frames_input,
        device_list=device_list,
        circle_interval=100,
        use_car_image=True,
        display_range=args.display_range,
        id_text_size=0.8,  # 使用较大的文本来更好地演示重叠问题
        dynamic_text_position=args.dynamic_text_position
    )

    # 设置缩放因子
    visualizer.scale_factor = args.scale_factor

    # 配置文本显示参数
    visualizer.configure_text_display(
        collision_detection=True,
        dynamic_size=True,
        text_background=True,
        min_size=0.4,
        max_size=1.2,
        offset_radius=25,
        spacing=3
    )

    # 默认启用目标旁边显示ID来演示重叠修复
    visualizer.show_id_at_target = True

    print("\n=== 文本重叠修复功能演示 ===")
    print("键盘控制:")
    print("  I - 切换ID显示模式（目标旁边 vs 右上角面板）")
    print("  T - 开启/关闭文本碰撞检测")
    print("  B - 开启/关闭文本背景")
    print("  +/- - 增加/减少文本大小")
    print("  R - 开启/关闭动态文本大小调整")
    print("  P - 开启/关闭动态文本位置功能（总开关）")
    print("  C - 切换车辆图像显示")
    print("  Space - 暂停/播放")
    print("  A/D - 后退/前进10帧")
    print("  ESC/Q - 退出")
    print("\n当前配置:")
    print(f"  动态文本位置: {'启用' if visualizer.dynamic_text_position else '禁用'}")
    print(f"  文本碰撞检测: {'启用' if visualizer.text_collision_detection else '禁用'}")
    print(f"  动态文本大小: {'启用' if visualizer.dynamic_text_size else '禁用'}")
    print(f"  文本背景: {'启用' if visualizer.text_background else '禁用'}")
    print(f"  文本大小: {visualizer.id_text_size}")
    print(f"  ID显示模式: {'目标旁边' if visualizer.show_id_at_target else '右上角面板'}")
    print("\n开始可视化...")

    # 开始可视化
    visualizer.create_visualization(show_window=args.show_window)


if __name__ == '__main__':
    main()
