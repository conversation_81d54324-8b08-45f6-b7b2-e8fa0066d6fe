# 轨迹接力可视化系统 - 目标ID文本重叠问题修复方案

## 问题分析

### 当前实现的问题
1. **固定偏移导致重叠**：所有目标ID都使用固定的偏移量（+20, +5），当目标密度高时必然重叠
2. **缺乏碰撞检测**：没有检测文本之间的重叠情况
3. **文本大小固定**：虽然`id_text_size`是参数，但不会根据目标密度动态调整
4. **简单的布局策略**：只考虑目标右侧显示，没有智能布局算法
5. **可读性差**：在复杂背景下文本难以阅读

## 解决方案

### 1. 智能文本布局算法

#### 多候选位置策略
- **右侧位置**（优先级最高）：目标右侧的3个位置
- **左侧位置**：目标左侧的3个位置  
- **上方位置**：目标上方的3个位置
- **下方位置**：目标下方的3个位置

#### 螺旋搜索算法
当所有候选位置都被占用时，使用螺旋搜索算法：
- 从基础半径开始，逐渐增大搜索半径
- 每个半径上按角度步长搜索可用位置
- 确保文本在帧边界内且不与现有文本重叠

### 2. 文本碰撞检测系统

#### 矩形重叠检测
```python
def _check_text_collision(self, new_rect, existing_rects):
    # 检查新文本矩形是否与已存在的文本矩形重叠
    # 考虑文本间距，确保足够的视觉分离
```

#### 实时碰撞避免
- 维护已绘制文本的矩形列表
- 为每个新文本寻找不重叠的位置
- 如果找不到合适位置，则跳过显示（避免重叠）

### 3. 动态文本大小调整

#### 基于目标密度的自适应调整
```python
def _calculate_dynamic_text_size(self, target_count):
    if target_count <= 5:
        return self.max_text_size      # 1.2
    elif target_count <= 15:
        return self.id_text_size       # 1.0
    elif target_count <= 25:
        return self.min_text_size + 0.2 # 0.6
    else:
        return self.min_text_size      # 0.4
```

### 4. 文本可读性增强

#### 半透明背景
- 为文本添加白色半透明背景
- 增加边框提高视觉分离度
- 可通过参数控制是否启用

#### 颜色对比优化
- 文本颜色与目标颜色保持一致
- 背景色确保足够的对比度

## 新增功能特性

### 1. 配置方法
```python
def configure_text_display(self, collision_detection=True, dynamic_size=True, 
                          text_background=True, min_size=0.4, max_size=1.2, 
                          offset_radius=30, spacing=5):
```

### 2. 实时控制
- **I键**：切换ID显示模式（目标旁边 vs 右上角面板）
- **T键**：开启/关闭文本碰撞检测
- **B键**：开启/关闭文本背景
- **+/-键**：增加/减少文本大小
- **R键**：开启/关闭动态文本大小调整

### 3. 参数化配置
```python
# 文本重叠避免相关参数
self.text_collision_detection = True  # 是否启用文本碰撞检测
self.dynamic_text_size = True         # 是否根据目标密度动态调整文本大小
self.text_background = True           # 是否为文本添加背景
self.min_text_size = 0.4             # 最小文本大小
self.max_text_size = 1.2             # 最大文本大小
self.text_offset_radius = 30         # 文本偏移的基础半径
self.text_spacing = 5                # 文本之间的最小间距
```

## 性能优化

### 1. 缓存机制
- 文本尺寸计算结果缓存
- 避免重复的OpenCV文本尺寸计算

### 2. 早期退出策略
- 优先尝试常用位置
- 找到合适位置后立即返回
- 避免不必要的搜索

### 3. 分层处理
- 先处理所有目标的绘制
- 再统一处理文本布局
- 减少重复计算

## 使用示例

### 基本使用
```python
# 创建可视化器
visualizer = Visualizer(...)

# 配置文本显示
visualizer.configure_text_display(
    collision_detection=True,
    dynamic_size=True,
    text_background=True
)

# 启用目标旁边显示ID
visualizer.show_id_at_target = True
```

### 测试脚本
```bash
# 运行测试脚本
python test_text_overlap_fix.py --show_window True

# 使用真实数据测试
python test_text_overlap_fix.py --log_file path/to/log.txt
```

## 兼容性保证

### 1. 向后兼容
- 所有新功能默认启用，但可以关闭
- 原有的简单布局仍然可用
- 不影响现有的可视化功能

### 2. 性能影响
- 新算法仅在启用文本碰撞检测时运行
- 对于目标数量较少的场景，性能影响微乎其微
- 对于高密度场景，显著改善可读性

### 3. 配置灵活性
- 所有参数都可以实时调整
- 支持不同场景的优化配置
- 提供合理的默认值

## 测试验证

### 1. 功能测试
- 高密度目标场景下的文本重叠避免
- 动态文本大小调整的效果
- 文本背景的可读性提升

### 2. 性能测试
- 大量目标时的处理速度
- 内存使用情况
- 实时交互的响应性

### 3. 用户体验测试
- 键盘控制的便利性
- 参数调整的直观性
- 视觉效果的改善程度

## 未来扩展

### 1. 更多布局算法
- 基于重要性的文本优先级
- 文本聚类和分组显示
- 自适应字体选择

### 2. 高级可读性优化
- 动态颜色调整
- 文本阴影效果
- 更多背景样式

### 3. 智能显示策略
- 基于用户关注点的选择性显示
- 文本淡入淡出动画
- 层次化信息显示
