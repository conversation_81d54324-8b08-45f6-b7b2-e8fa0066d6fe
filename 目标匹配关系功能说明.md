# 目标匹配关系功能说明

## 功能概述

新增的目标匹配关系功能用于建立输入目标和输出目标（融合目标）之间的对应关系，帮助分析轨迹接力算法的性能。

## 核心功能

### 1. 函数：`build_target_matching_relationships()`

**功能描述**：
- 分析输入轨迹点和输出轨迹点数据
- 建立融合目标与输入目标的映射关系
- 为每个输入目标添加匹配状态标记

**参数**：
- `track_input_point_frame_list`: 输入轨迹点帧列表
- `track_output_point_frame_list`: 输出轨迹点帧列表

**返回值**：
- `fusion_to_input_map`: 融合目标到输入目标的映射字典
  - 格式：`{fusion_id: set((device_id, target_id), ...)}`
- `updated_input_frames`: 更新后的输入目标列表（包含is_matched属性）

### 2. 匹配逻辑

**匹配规则**：
- 当输入目标的 `device_id` 和原始 `target_id` 与输出目标中的对应字段相同时，认为匹配成功
- 考虑到target_id的转换（`device_id * 1000 + original_target_id`），函数会自动还原原始ID进行匹配

**数据结构**：
- 输入目标：splice_id为字符串格式 `"{target_id}_{device_id}"`
- 输出目标：splice_id为数字格式，表示融合后的目标ID

### 3. 新增属性

**TrackPoint.is_matched**：
- 类型：布尔值
- 含义：标记该输入目标是否参与了某个融合目标的形成
- 用途：可用于可视化显示、统计分析等

## 使用示例

### 基本使用

```python
# 解析输入和输出数据
track_input_frames = parse_log_track_point_input(log_file)
track_output_frames = parse_log_track_point_output(log_file)

# 建立匹配关系
fusion_to_input_map, updated_input_frames = build_target_matching_relationships(
    track_input_frames, track_output_frames)

# 查看匹配结果
for fusion_id, input_targets in fusion_to_input_map.items():
    print(f"融合目标 {fusion_id} 包含输入目标: {input_targets}")

# 检查输入目标匹配状态
for frame in updated_input_frames:
    for target in frame:
        if target.is_matched:
            print(f"目标 {target.splice_id} 已匹配")
        else:
            print(f"目标 {target.splice_id} 未匹配")
```

### 统计分析

```python
# 统计匹配率
total_input_targets = sum(len(frame) for frame in updated_input_frames)
matched_input_targets = sum(1 for frame in updated_input_frames 
                           for target in frame if target.is_matched)
match_rate = matched_input_targets / total_input_targets * 100

print(f"总输入目标数: {total_input_targets}")
print(f"已匹配目标数: {matched_input_targets}")
print(f"匹配率: {match_rate:.1f}%")
```

## 集成到主程序

该功能已自动集成到主程序中，在数据解析完成后自动执行：

1. 解析输入轨迹点数据
2. 解析输出轨迹点数据
3. **建立目标匹配关系**（新增）
4. 创建可视化器
5. 进行统计分析和可视化

## 输出信息

程序运行时会显示匹配关系建立的进度和结果：

```
正在建立目标匹配关系...
匹配关系建立完成:
  - 融合目标数量: 150
  - 输入目标总数: 500
  - 已匹配输入目标: 400
  - 匹配率: 80.0%
```

## 应用场景

1. **算法性能评估**：分析有多少输入目标成功参与了融合
2. **数据质量检查**：识别未被融合的孤立目标
3. **可视化增强**：可根据匹配状态使用不同颜色显示目标
4. **统计分析**：为准确率和召回率计算提供基础数据

## 注意事项

1. 函数会自动处理target_id的转换，无需手动调整
2. 匹配关系基于device_id和原始target_id的组合
3. 输入目标的is_matched属性在函数执行后才会被设置
4. 融合目标ID为0的目标会被自动跳过
