import math

def calculate_distance(point1, point2):
    from math import radians, sin, cos, sqrt, atan2
    # 地球平均半径(米)
    R = 6371000

    # 将经纬度转换为弧度
    lon1, lat1 = map(radians, point1)
    lon2, lat2 = map(radians, point2)

    # 计算经纬度差值
    dlon = lon2 - lon1
    dlat = lat2 - lat1

    # Haversine公式
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    distance = R * c

    return distance

def lonlat_to_mercator(longitude, latitude):
    """
    将经纬度坐标转换为墨卡托投影坐标系下的坐标

    参数:
        longitude: 经度值(度)
        latitude: 纬度值(度)

    返回:
        (x, y): 墨卡托坐标系下的坐标(米)
    """
    # 地球半径(米)
    R = 6378137.0

    # 检查经纬度是否在有效范围内
    # 经度范围：-180到180度
    # 纬度范围：-85.05112878到85.05112878度（墨卡托投影的有效范围）
    longitude = max(min(longitude, 180.0), -180.0)
    latitude = max(min(latitude, 85.0), -85.0)

    # 将经纬度转换为弧度
    lon_rad = math.radians(longitude)
    lat_rad = math.radians(latitude)

    try:
        # 计算墨卡托坐标
        x = R * lon_rad
        y = R * math.log(math.tan(math.pi/4 + lat_rad/2))
        return (x, y)
    except ValueError:
        # 如果计算出错，返回一个默认值
        print(f"警告：经纬度 ({longitude}, {latitude}) 转换为墨卡托坐标时出错，使用默认值")
        return (0, 0)

