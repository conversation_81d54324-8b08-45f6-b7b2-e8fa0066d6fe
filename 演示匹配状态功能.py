#!/usr/bin/env python3
"""
演示匹配状态可视化功能的使用方法
"""

def print_feature_demo():
    """打印功能演示说明"""
    print("=" * 60)
    print("🎯 轨迹接力可视化系统 - 匹配状态显示功能演示")
    print("=" * 60)
    
    print("\n📋 功能概述:")
    print("   新增的匹配状态可视化功能允许通过颜色区分输入目标的匹配状态")
    print("   - 绿色：已匹配的输入目标（参与了融合目标的形成）")
    print("   - 红色：未匹配的输入目标（未参与融合目标的形成）")
    
    print("\n🎮 使用方法:")
    print("   1. 运行主程序：")
    print("      python 轨迹接力可视化.py")
    print("   ")
    print("   2. 在可视化窗口中按键 M 切换匹配状态显示模式")
    print("   ")
    print("   3. 观察左上角状态指示器的变化：")
    print("      - Matching Status: OFF (默认模式，基于ID的颜色)")
    print("      - Matching Status: ON  (匹配状态模式，绿色/红色)")
    
    print("\n⌨️  完整键盘控制:")
    controls = [
        ("Space", "暂停/播放"),
        ("A/D", "后退/前进"),
        ("C", "车辆图像/圆点切换"),
        ("I", "ID显示模式切换"),
        ("M", "匹配状态显示切换 ← 新功能"),
        ("T", "文本碰撞检测切换"),
        ("B", "文本背景切换"),
        ("+/-", "文本大小调整"),
        ("P", "动态文本位置切换"),
        ("ESC/Q", "退出程序")
    ]
    
    for key, desc in controls:
        print(f"      {key:<8} - {desc}")
    
    print("\n🎨 颜色说明:")
    print("   启用匹配状态显示时：")
    print("   ┌─────────────────────────────────────────┐")
    print("   │ 左侧区域（输入目标）：                    │")
    print("   │   🟢 绿色 = 已匹配目标                   │")
    print("   │   🔴 红色 = 未匹配目标                   │")
    print("   │                                         │")
    print("   │ 右侧区域（输出目标）：                    │")
    print("   │   🌈 保持原有ID颜色                      │")
    print("   └─────────────────────────────────────────┘")
    
    print("\n📊 应用场景:")
    scenarios = [
        "算法性能分析：快速识别融合成功率",
        "数据质量检查：发现孤立或异常目标",
        "调试和优化：实时观察参数调整效果",
        "效果对比：在不同颜色模式间切换对比"
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"   {i}. {scenario}")
    
    print("\n💡 使用技巧:")
    tips = [
        "先运行程序确保目标匹配关系已建立",
        "在播放过程中随时按M键切换模式",
        "观察不同时间段的匹配率变化",
        "结合统计面板数据进行综合分析"
    ]
    
    for tip in tips:
        print(f"   • {tip}")
    
    print("\n🔧 技术细节:")
    print("   • 功能默认关闭，需手动启用")
    print("   • 只影响左侧输入目标显示")
    print("   • 与所有现有功能完全兼容")
    print("   • 基于目标的is_matched属性判断")
    
    print("\n📈 预期效果:")
    print("   启用匹配状态显示后，您将看到：")
    print("   ┌─────────────────────────────────────────┐")
    print("   │ Matching Status: ON                     │")
    print("   │ Green=Matched, Red=Unmatched            │")
    print("   └─────────────────────────────────────────┘")
    print("   ")
    print("   终端输出：")
    print("   Matching status display: Enabled")
    print("     - Input targets: Green=Matched, Red=Unmatched")
    print("     - Output targets: Original colors")
    
    print("\n" + "=" * 60)
    print("🚀 准备好体验新功能了吗？运行主程序并按M键试试看！")
    print("=" * 60)

def print_example_usage():
    """打印使用示例"""
    print("\n📝 使用示例:")
    print("   # 运行主程序")
    print("   python 轨迹接力可视化.py")
    print("   ")
    print("   # 或者只进行统计（不显示可视化）")
    print("   python 轨迹接力可视化.py --stats_only")
    print("   ")
    print("   # 自定义参数运行")
    print("   python 轨迹接力可视化.py \\")
    print("       --distance_threshold 800 \\")
    print("       --max_targets 100 \\")
    print("       --dynamic_text_position True")

if __name__ == "__main__":
    print_feature_demo()
    print_example_usage()
    
    print("\n🎯 想要立即测试吗？")
    response = input("输入 'y' 运行主程序，或按回车键退出: ").strip().lower()
    
    if response == 'y':
        import subprocess
        import sys
        try:
            print("\n🚀 启动轨迹接力可视化系统...")
            subprocess.run([sys.executable, "轨迹接力可视化.py"], check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ 启动失败: {e}")
        except FileNotFoundError:
            print("❌ 找不到主程序文件 '轨迹接力可视化.py'")
        except KeyboardInterrupt:
            print("\n👋 用户中断，程序退出")
    else:
        print("👋 感谢使用！")
