import cv2
import numpy as np
import argparse
import os
import time
import math
import matplotlib.pyplot as plt
import pandas as pd
from objs.LaneLine import LaneLine
from objs.TrackPoint import TrackPoint
from objs.Visualizer import Visualizer
from utils import lonlat_to_mercator

# 从日志中解析车道线信息
def parse_log_lane_line(file_path):
    """
    解析日志文件中的车道线数据。

    从指定日志文件中提取车道线信息，包括车道线ID、坐标点数量以及每个坐标点的经纬度。
    返回包含所有车道线数据的列表，每个车道线对象包含其ID、坐标点数量和坐标点列表。

    参数:
        file_path (str): 要解析的日志文件路径

    返回:
        list[LaneLine]: 包含所有车道线对象的列表，每个对象包含:
            - lane_line_id: 车道线ID
            - size: 坐标点数量
            - points: 坐标点列表(经度,纬度)
    """
    """
    解析日志文件，提取车道线数据和轨迹点数据。

    返回：

    """
    # 解析车道线
    lane_line_list = []
    lane_line = None
    read_status = -1    # -1：等待读取；0：创建车道线对象； 1：读取该车道线下的坐标点
    d = 4

    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        for line in f:
            # 检测时间戳行
            if 'road_config lane_vec coordinate_vec:' in line :
                read_status = 0
                continue
            if "lane_line coordinate_vec size" in line:
                if lane_line is not None:
                    lane_line_list.append(lane_line)
                    lane_line = None
                parts = line.strip().split()
                lane_line_id = int(parts[0+d].split(':')[1].split(':')[0].split('/')[0])
                lane_line_size = int(parts[3+d].split(':')[1])
                lane_line = LaneLine(lane_line_id, lane_line_size)
                read_status = 1
                continue
            if 'load_line_size_end' in line:
                if lane_line is not None:
                    lane_line_list.append(lane_line)
                    lane_line = None
                    break
            if read_status == 1:
                # 读取车道线坐标点
                parts = line.strip().split()
                longitude = float(parts[3+d].split(':')[1])
                latitude = float(parts[2+d].split(':')[1])
                lane_line.points.append((longitude, latitude))
    return lane_line_list

def parse_log_track_point_output(file_path):
    d = 4
    read_status = -1
    track_point_frame_list = []
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        for line in f:
            if 'multi_boxes_output: splicing_output_targets target_cnt:' in line:
                read_status = 0
                track_point_list = []
                continue
            # 读取每个目标
            if read_status == 0 and 'multi_boxes_output: splice_id' in line:
                parts = line.strip().split()
                splice_id = int(parts[1+d].split(':')[1])
                target_id = int(parts[3+d].split(':')[1])
                device_id = int(parts[2+d].split(':')[1])
                timestamp_ms = int(parts[10+d].split(':')[1])
                is_pass_ovd = 0
                longitude = float(parts[8+d].split(':')[1])
                latitude = float(parts[9+d].split(':')[1])
                longitude_update = float(parts[6+d].split(':')[1])
                latitude_update = float(parts[7+d].split(':')[1])
                target_id = device_id * 1000 + target_id
                track_point = TrackPoint(
                    splice_id = splice_id,
                    target_id = target_id,
                    device_id = device_id,
                    timestamp_ms = timestamp_ms,
                    is_pass_ovd = is_pass_ovd,
                    longitude = longitude,
                    latitude = latitude,
                    longitude_update = longitude_update,
                    latitude_update = latitude_update
                )
                track_point_list.append(track_point)
            if ' output end ' in line:
                track_point_frame_list.append(track_point_list)
                read_status = -1
    return track_point_frame_list

def parse_log_track_point_input(file_path):
    d = 4
    read_status = -1
    track_point_frame_list = []
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        for line in f:
            if 'splicing_input_target_vec splicing_input_target_vec.size:' in line:
                read_status = 0
                track_point_list = []
                continue
            # 读取每个目标
            if read_status == 0 and 'splicing_input_target_vec id:' in line:
                parts = line.strip().split()
                splice_id = int(parts[6].split(':')[1])
                target_id = int(parts[5].split(':')[1])
                device_id = int(parts[7].split(':')[1])
                timestamp_ms = int(parts[27].split(':')[1])
                is_pass_ovd = 0
                longitude = float(parts[10].split(':')[1])
                latitude = float(parts[11].split(':')[1])
                longitude_update = float(parts[10].split(':')[1])
                latitude_update = float(parts[11].split(':')[1])
                splice_id = f'{target_id}_{device_id}'
                target_id = device_id * 1000 + target_id
                track_point = TrackPoint(
                    splice_id = splice_id,
                    target_id = target_id,
                    device_id = device_id,
                    timestamp_ms = timestamp_ms,
                    is_pass_ovd = is_pass_ovd,
                    longitude = longitude,
                    latitude = latitude,
                    longitude_update = longitude_update,
                    latitude_update = latitude_update
                )
                # 过滤掉不需要的轨迹点
                if float(parts[16].split(":")[-1]) < 0 and device_id == 4:
                    continue
                track_point_list.append(track_point)
            if 'splicing_input_target_vec end' in line:
                track_point_frame_list.append(track_point_list)
                read_status = -1
    return track_point_frame_list

def build_target_matching_relationships(track_input_point_frame_list, track_output_point_frame_list):
    """
    建立输入目标和输出目标之间的匹配关系

    参数:
        track_input_point_frame_list: 输入轨迹点帧列表
        track_output_point_frame_list: 输出轨迹点帧列表

    返回:
        tuple: (fusion_to_input_map, updated_input_frames)
            - fusion_to_input_map: 输出目标到输入目标的映射字典 {fusion_id: set((device_id, target_id), ...)}
            - updated_input_frames: 更新后的输入目标列表（包含is_matched属性）
    """
    # 输出目标到输入目标的映射字典
    fusion_to_input_map = {}

    # 收集所有输出目标及其包含的原始目标ID
    for frame_idx, frame in enumerate(track_output_point_frame_list):
        for target in frame:
            # 跳过splice_id为0的目标
            if target.splice_id == 0:
                continue

            fusion_id = str(target.splice_id)

            if fusion_id not in fusion_to_input_map:
                fusion_to_input_map[fusion_id] = set()

            # 将输出目标的device_id和target_id添加到映射中
            # 注意：这里的target_id已经是经过转换的(device_id * 1000 + original_target_id)
            # 需要还原原始的target_id进行匹配
            original_target_id = target.target_id - target.device_id * 1000
            fusion_to_input_map[fusion_id].add((target.device_id, original_target_id))

    # 为输入目标添加is_matched属性
    updated_input_frames = []
    for frame_idx, frame in enumerate(track_input_point_frame_list):
        updated_frame = []
        for target in frame:
            # 还原原始target_id进行匹配
            original_target_id = target.target_id - target.device_id * 1000
            target_key = (target.device_id, original_target_id)

            # 检查该输入目标是否在任何融合目标中
            is_matched = False
            for fusion_id, input_targets in fusion_to_input_map.items():
                if target_key in input_targets:
                    is_matched = True
                    break

            # 为目标添加is_matched属性
            target.is_matched = is_matched
            updated_frame.append(target)

        updated_input_frames.append(updated_frame)

    return fusion_to_input_map, updated_input_frames

# 解析日志文件，提取车道线数据和轨迹点数据
def main():
    # 解析命令行参数
    # python 轨迹接力可视化.py --stats_only
    """
    治超检测可视化工具主函数

    功能：
        1. 解析命令行参数，包括日志文件路径、输出设置、显示参数等
        2. 检查输入文件有效性
        3. 解析车道线和轨迹点数据
        4. 根据参数选择运行模式：
            - 统计模式：计算并输出接力目标统计结果
            - 可视化模式：生成治超检测可视化视频

    参数：
        通过argparse解析命令行参数，包括：
        --log_file: 主日志文件路径
        --log_lane_file: 车道线日志文件路径
        --output_dir: 输出目录
        --show_window: 是否显示实时窗口
        --stats_only: 是否仅运行统计模式
        --distance_threshold: 准确率计算距离阈值
        等完整参数列表见代码

    返回值：
        无返回值，根据模式输出统计结果或生成可视化文件
    """
    parser = argparse.ArgumentParser(description='治超检测可视化工具')
    parser.add_argument('--log_file', type=str,
                        default='/home/<USER>/project/TECU1000/RV/tecu1000_0_algorithm/algorithm/log/tecu_1000_0.log',
                        # default='/home/<USER>/project/TECU1000/TECU1000_0/tecu1000_0_algorithm/algorithm/log/tecu_1000_0.log',
                        # default='log/tecu_1000_0.log',
                        help='日志文件路径')
    parser.add_argument('--log_lane_file', type=str,
                        default='lane.log',
                        help='日志文件路径')
    parser.add_argument('--output_dir', type=str, default=None, help='输出目录')
    parser.add_argument('--output_name', type=str, default='治超检测可视化.avi', help='输出文件名')
    parser.add_argument('--show_window', type=bool, default=True, help='是否显示窗口（可能导致线程问题）')
    parser.add_argument('--scale_factor', type=float, default=2.0, help='缩放因子')
    parser.add_argument('--display_range', type=float, default=1500.0, help='显示的总范围（米）')
    parser.add_argument('--base_lon', type=float, default=112.989427, help='基准点经度')
    parser.add_argument('--base_lat', type=float, default=28.090128, help='基准点纬度')
    parser.add_argument('--circle_interval', type=float, default=100.0, help='距离圆的间隔（米）')
    parser.add_argument('--use_car_image', type=bool, default=False, help='是否使用车辆图片替代圆点')
    parser.add_argument('--distance_threshold', type=float, default=100.0, help='准确率计算的前进距离阈值（米）')
    parser.add_argument('--recall_distance_threshold', type=float, default=100.0, help='召回率计算的前进距离阈值（米）')
    parser.add_argument('--stats_only', action='store_true', help='仅进行统计，不显示可视化界面')
    parser.add_argument('--stats_output_dir', type=str, default='stats', help='统计结果输出目录')
    parser.add_argument('--max_targets', type=int, default=100, help='用于统计的最大融合目标数量，默认为100，设为0表示不限制')
    parser.add_argument('--min_device_count', type=int, default=4, help='有效目标的最小设备数量要求，默认为2')
    parser.add_argument('--dynamic_text_position', type=bool, default=False, help='是否启用动态文本位置功能（智能布局算法），默认为True。设为False时使用简单固定偏移布局')

    args = parser.parse_args()

    # 检查日志文件是否存在
    if not os.path.exists(args.log_file):
        print(f"错误：日志文件 {args.log_file} 不存在")
        return

    # 设置基准点
    base_point = (args.base_lon, args.base_lat)  # 墨卡托坐标系的基坐标系对应的经纬度 也是绘图的中心点
    base_point_mercator = lonlat_to_mercator(base_point[0], base_point[1])

    # 解析数据
    print("正在解析车道线数据...")
    lane_line_list = parse_log_lane_line(args.log_lane_file)
    print(f"解析到 {len(lane_line_list)} 条车道线")

    print("正在解析输出轨迹点数据...")
    track_output_point_frame_list = parse_log_track_point_output(args.log_file)
    print(f"解析到 {len(track_output_point_frame_list)} 帧轨迹数据")

    print("正在解析输入轨迹点数据...")
    track_input_point_frame_list = parse_log_track_point_input(args.log_file)
    print(f"解析到 {len(track_input_point_frame_list)} 帧轨迹数据")

    # 建立输入目标和输出目标之间的匹配关系
    print("正在建立目标匹配关系...")
    fusion_to_input_map, updated_input_frames = build_target_matching_relationships(
        track_input_point_frame_list, track_output_point_frame_list)

    # 统计匹配结果
    total_fusion_targets = len(fusion_to_input_map)
    total_input_targets = sum(len(frame) for frame in updated_input_frames)
    matched_input_targets = sum(1 for frame in updated_input_frames
                               for target in frame if target.is_matched)

    print(f"匹配关系建立完成:")
    print(f"  - 融合目标数量: {total_fusion_targets}")
    print(f"  - 输入目标总数: {total_input_targets}")
    print(f"  - 已匹配输入目标: {matched_input_targets}")
    print(f"  - 匹配率: {matched_input_targets/total_input_targets*100:.1f}%" if total_input_targets > 0 else "  - 匹配率: 0.0%")

    # 使用更新后的输入目标数据
    track_input_point_frame_list = updated_input_frames

    # 设备列表
    device_list = [(112.992244, 28.089316),
                    (112.992244, 28.089316),
                    (112.98661 , 28.09094),
                    (112.98661 , 28.090939)]

    if len(lane_line_list) == 0:
        print("警告：未解析到车道线数据")

    if len(track_output_point_frame_list) == 0:
        print("警告：未解析到轨迹点数据")

    # 创建可视化器
    visualizer = Visualizer(base_point, base_point_mercator, lane_line_list,
                            track_output_point_frame_list, track_input_point_frame_list,
                            device_list=device_list, circle_interval=args.circle_interval,
                            use_car_image=args.use_car_image, display_range=args.display_range,
                            dynamic_text_position=args.dynamic_text_position)

    # 设置缩放因子
    visualizer.scale_factor = args.scale_factor


    # 设置接力目标前进距离阈值、最大目标数量、召回率距离阈值和最小设备数量
    visualizer.set_distance_threshold(
        threshold=args.distance_threshold,
        max_targets=args.max_targets,
        recall_distance_threshold=args.recall_distance_threshold,
        min_device_count=args.min_device_count
    )

    # 获取统计对象
    statistics = visualizer.get_statistics()

    # 打印统计结果
    statistics.print_statistics_summary()

    # 保存统计结果到Excel
    statistics.save_statistics_to_excel(args.stats_output_dir)
    # 设置输出路径
    output_dir = args.output_dir
    if output_dir is not None:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        output_path = os.path.join(output_dir, args.output_name)
    else:
        output_path = None
    # 如果是统计模式，设置距离阈值并进行统计
    if args.stats_only:
        return
    # 创建可视化
    print("开始生成可视化...")
    visualizer.create_visualization(output_path=output_path, show_window=args.show_window)



if __name__ == '__main__':
    main()
