import cv2
import numpy as np
import argparse
import os
import time
import math
import matplotlib.pyplot as plt
import pandas as pd
from utils import lonlat_to_mercator
from objs.RelayStatistics import RelayStatistics


class Visualizer:
    """
    可视化类，负责绘制车道线和目标轨迹
    """
    def __init__(self, base_point, base_point_mercator, lane_line_list,
                 track_output_point_frame_list, track_input_point_frame_list,
                 device_list=[], circle_interval=20, use_car_image=True, display_range=500.0, id_text_size=1.0, dynamic_text_position=True):
        """
        初始化可视化器

        参数:
            base_point: 基准点经纬度坐标
            base_point_mercator: 基准点墨卡托坐标
            lane_line_list: 车道线列表
            track_output_point_frame_list: 右侧区域轨迹点帧列表
            track_input_point_frame_list: 左侧区域轨迹点帧列表
            device_list: 设备列表
            circle_interval: 距离圆的间隔（米），默认为20米
            use_car_image: 是否使用车辆图片替代圆点，默认为True
            display_range: 显示的总范围（米），默认为500米
            id_text_size: 目标ID文本的大小，默认为1.0
            dynamic_text_position: 是否启用动态文本位置功能，默认为True
        """
        self.base_point = base_point
        self.base_point_mercator = base_point_mercator
        self.lane_line_list = lane_line_list
        self.track_output_frames = track_output_point_frame_list
        self.track_input_frames = track_input_point_frame_list
        self.device_list = device_list
        self.circle_interval = circle_interval
        self.use_car_image = use_car_image
        self.display_range = display_range
        self.id_text_size = id_text_size  # 目标ID文本的大小
        self.show_id_at_target = False  # 默认在右上角面板显示ID，而不是在目标旁边

        # 动态文本位置控制
        self.dynamic_text_position = dynamic_text_position  # 是否启用动态文本位置功能

        # 文本重叠避免相关参数（仅在dynamic_text_position为True时生效）
        self.text_collision_detection = dynamic_text_position  # 是否启用文本碰撞检测
        self.dynamic_text_size = dynamic_text_position  # 是否根据目标密度动态调整文本大小
        self.text_background = True  # 是否为文本添加背景
        self.min_text_size = 0.4  # 最小文本大小
        self.max_text_size = 1.2  # 最大文本大小
        self.text_offset_radius = 30  # 文本偏移的基础半径
        self.text_spacing = 5  # 文本之间的最小间距

        # 匹配状态显示控制
        self.show_matching_status = False  # 是否启用匹配状态显示模式，默认关闭

        # 初始化缩放因子
        self._scale_factor = 1.0
        # 根据显示范围和缩放因子计算画布大小
        self._update_canvas_size()
        self.target_colors = {}

        # 初始化坐标转换缓存
        self._coords_cache = {}
        # 初始化车道线缓存
        self._lane_lines_cache = {'left': {}, 'right': {}}
        # 初始化设备位置缓存
        self._device_coords_cache = {'left': [], 'right': []}

        self.history_tracks_left = {}
        self.history_tracks_right = {}
        self.max_history_length = 20
        self.line_width = 2

        # 加载车辆图像
        self.car_img = cv2.imread('assets/car.png', cv2.IMREAD_UNCHANGED)
        self.car_size = 40  # 设置固定的车辆图像大小

        if self.car_img is None:
            print("Warning: Unable to load car icon 'assets/car.png', will use default circle marker")
        else:
            if self.car_img.shape[2] == 4:
                b, g, r, a = cv2.split(self.car_img)
                self.car_img = cv2.merge((b, g, r))
                self.car_alpha = a
                # 预先调整大小
                self.resized_car = cv2.resize(self.car_img, (self.car_size, self.car_size))
                self.resized_alpha = cv2.resize(self.car_alpha, (self.car_size, self.car_size))
            else:
                self.car_alpha = None
                # 预先调整大小
                self.resized_car = cv2.resize(self.car_img, (self.car_size, self.car_size))
                self.resized_alpha = None

        # 初始化接力统计对象
        self.relay_statistics = None  # 将在设置distance_threshold时初始化

        # 预处理车道线和设备位置
        self._preprocess_lane_lines()
        self._preprocess_device_positions()

    def configure_text_display(self, collision_detection=True, dynamic_size=True,
                              text_background=True, min_size=0.4, max_size=1.2,
                              offset_radius=30, spacing=5):
        """
        配置目标ID文本显示参数

        注意：碰撞检测和动态大小功能仅在dynamic_text_position为True时生效

        参数:
            collision_detection: 是否启用文本碰撞检测（需要dynamic_text_position=True）
            dynamic_size: 是否根据目标密度动态调整文本大小（需要dynamic_text_position=True）
            text_background: 是否为文本添加背景
            min_size: 最小文本大小
            max_size: 最大文本大小
            offset_radius: 文本偏移的基础半径
            spacing: 文本之间的最小间距
        """
        # 只有在启用动态文本位置时，碰撞检测和动态大小功能才生效
        if self.dynamic_text_position:
            self.text_collision_detection = collision_detection
            self.dynamic_text_size = dynamic_size
        else:
            self.text_collision_detection = False
            self.dynamic_text_size = False

        self.text_background = text_background
        self.min_text_size = min_size
        self.max_text_size = max_size
        self.text_offset_radius = offset_radius
        self.text_spacing = spacing

    def toggle_id_display_mode(self):
        """
        切换目标ID显示模式（目标旁边 vs 右上角面板）

        返回:
            str: 当前显示模式的描述
        """
        self.show_id_at_target = not self.show_id_at_target
        return "At Target" if self.show_id_at_target else "Panel"

    def set_text_size(self, size):
        """
        设置目标ID文本大小

        参数:
            size: 文本大小（浮点数）
        """
        self.id_text_size = max(0.1, min(3.0, size))  # 限制在合理范围内

    def toggle_dynamic_text_position(self):
        """
        切换动态文本位置功能

        返回:
            str: 当前动态文本位置功能状态的描述
        """
        self.dynamic_text_position = not self.dynamic_text_position

        # 根据新状态更新相关功能
        if self.dynamic_text_position:
            # 启用动态文本位置时，恢复智能功能
            self.text_collision_detection = True
            self.dynamic_text_size = True
        else:
            # 禁用动态文本位置时，关闭智能功能，回退到简单模式
            self.text_collision_detection = False
            self.dynamic_text_size = False

        return "Enabled" if self.dynamic_text_position else "Disabled"

    def get_text_display_status(self):
        """
        获取当前文本显示功能的状态信息

        返回:
            dict: 包含各种文本显示功能状态的字典
        """
        return {
            'dynamic_text_position': self.dynamic_text_position,
            'text_collision_detection': self.text_collision_detection,
            'dynamic_text_size': self.dynamic_text_size,
            'text_background': self.text_background,
            'show_id_at_target': self.show_id_at_target,
            'id_text_size': self.id_text_size,
            'min_text_size': self.min_text_size,
            'max_text_size': self.max_text_size
        }

    def _update_canvas_size(self):
        """
        根据显示范围和缩放因子更新画布大小
        """
        # 计算画布宽度和高度，保持像素密度不变
        canvas_width = int(self.display_range * 2 * self._scale_factor)
        canvas_height = int(self.display_range * self._scale_factor)
        self.canvas_size = (canvas_width, canvas_height)
        # 更新左右区域
        self.left_region = (0, 0, self.canvas_size[0] // 2, self.canvas_size[1])
        self.right_region = (self.canvas_size[0] // 2, 0, self.canvas_size[0], self.canvas_size[1])

        # 清空所有缓存，因为画布大小变化会影响所有坐标计算
        self._clear_all_caches()

    def _clear_all_caches(self):
        """
        清空所有缓存
        """
        self._coords_cache = {}
        self._lane_lines_cache = {'left': {}, 'right': {}}
        self._device_coords_cache = {'left': [], 'right': []}
        if hasattr(self, '_distance_circles_cache'):
            self._distance_circles_cache = {'left': [], 'right': []}

    @property
    def scale_factor(self):
        return self._scale_factor

    @scale_factor.setter
    def scale_factor(self, value):
        self._scale_factor = value
        self._update_canvas_size()  # 这里会调用_clear_all_caches()
        # 重新预处理静态数据
        self._preprocess_lane_lines()
        self._preprocess_device_positions()

    def _preprocess_lane_lines(self):
        """
        预处理车道线坐标，提前计算并缓存
        """
        if not self.lane_line_list:
            return

        for region_key in ['left', 'right']:
            for lane_line in self.lane_line_list:
                if not lane_line.points:
                    continue

                valid_points = []
                for lon, lat in lane_line.points:
                    try:
                        mercator_coords = lonlat_to_mercator(lon, lat)
                        if mercator_coords == (0, 0):
                            continue
                        canvas_coords = self._convert_to_canvas_coords(mercator_coords, region_key)
                        valid_points.append(canvas_coords)
                    except Exception:
                        continue

                if valid_points:
                    self._lane_lines_cache[region_key][lane_line.id] = valid_points

    def _preprocess_device_positions(self):
        """
        预处理设备位置坐标，提前计算并缓存
        """
        if not self.device_list:
            return

        for region_key in ['left', 'right']:
            device_coords = []
            for device in self.device_list:
                mercator_coords = lonlat_to_mercator(device[0], device[1])
                if mercator_coords == (0, 0):
                    continue
                canvas_coords = self._convert_to_canvas_coords(mercator_coords, region_key)
                device_coords.append(canvas_coords)

            self._device_coords_cache[region_key] = device_coords

    def set_distance_threshold(self, threshold, max_targets=100, recall_distance_threshold=100.0, min_device_count=2):
        """
        设置接力目标前进距离阈值，并初始化接力统计对象

        参数:
            threshold: 前进距离阈值（米）
            max_targets: 用于统计的最大融合目标数量，默认为100，设为0表示不限制
            recall_distance_threshold: 召回率计算中的距离阈值（米），默认为100米
            min_device_count: 有效目标的最小设备数量要求，默认为2
        """
        # 初始化接力统计对象
        self.relay_statistics = RelayStatistics(
            track_input_frames=self.track_input_frames,
            track_output_frames=self.track_output_frames,
            distance_threshold=threshold,
            max_targets=max_targets,
            recall_distance_threshold=recall_distance_threshold,
            min_device_count=min_device_count
        )

    def get_statistics(self):
        """
        获取接力统计对象

        返回:
            RelayStatistics: 接力统计对象
        """
        return self.relay_statistics

    def toggle_matching_status_display(self):
        """
        切换匹配状态显示模式

        返回:
            str: 当前状态描述
        """
        self.show_matching_status = not self.show_matching_status
        return "Enabled" if self.show_matching_status else "Disabled"

    def _calculate_text_size(self, text):
        """
        计算文本的像素尺寸

        参数:
            text: 要计算的文本字符串

        返回:
            tuple: (width, height) 文本的像素尺寸
        """
        font_scale = self.id_text_size
        thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(
            text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)
        return text_width, text_height + baseline

    def _check_text_collision(self, new_rect, existing_rects):
        """
        检查新文本矩形是否与已存在的文本矩形重叠

        参数:
            new_rect: 新文本的矩形 (x, y, width, height)
            existing_rects: 已存在的文本矩形列表

        返回:
            bool: True表示有重叠，False表示无重叠
        """
        x1, y1, w1, h1 = new_rect
        for x2, y2, w2, h2 in existing_rects:
            # 检查矩形是否重叠，添加间距
            if not (x1 + w1 + self.text_spacing < x2 or
                   x2 + w2 + self.text_spacing < x1 or
                   y1 + h1 + self.text_spacing < y2 or
                   y2 + h2 + self.text_spacing < y1):
                return True
        return False

    def _find_optimal_text_position(self, target_pos, text, frame_shape, existing_rects):
        """
        为目标ID文本找到最优的显示位置，避免重叠

        参数:
            target_pos: 目标的位置 (x, y)
            text: 要显示的文本
            frame_shape: 帧的形状 (height, width, channels)
            existing_rects: 已存在的文本矩形列表

        返回:
            tuple: (text_x, text_y) 文本的最优位置，如果找不到合适位置则返回None
        """
        text_width, text_height = self._calculate_text_size(text)
        target_x, target_y = target_pos

        # 定义多个候选位置（按优先级排序）
        candidate_positions = [
            # 右侧位置（优先）
            (target_x + self.text_offset_radius, target_y),
            (target_x + self.text_offset_radius, target_y - text_height),
            (target_x + self.text_offset_radius, target_y + text_height),

            # 左侧位置
            (target_x - self.text_offset_radius - text_width, target_y),
            (target_x - self.text_offset_radius - text_width, target_y - text_height),
            (target_x - self.text_offset_radius - text_width, target_y + text_height),

            # 上方位置
            (target_x - text_width // 2, target_y - self.text_offset_radius),
            (target_x, target_y - self.text_offset_radius),
            (target_x - text_width, target_y - self.text_offset_radius),

            # 下方位置
            (target_x - text_width // 2, target_y + self.text_offset_radius),
            (target_x, target_y + self.text_offset_radius),
            (target_x - text_width, target_y + self.text_offset_radius),
        ]

        # 尝试每个候选位置
        for text_x, text_y in candidate_positions:
            # 检查是否在帧边界内
            if (text_x >= 0 and text_y >= 0 and
                text_x + text_width < frame_shape[1] and
                text_y + text_height < frame_shape[0]):

                # 检查是否与现有文本重叠
                new_rect = (text_x, text_y, text_width, text_height)
                if not self._check_text_collision(new_rect, existing_rects):
                    return text_x, text_y

        # 如果所有候选位置都不合适，尝试螺旋搜索
        return self._spiral_search_text_position(target_pos, text, frame_shape, existing_rects)

    def _spiral_search_text_position(self, target_pos, text, frame_shape, existing_rects):
        """
        使用螺旋搜索算法寻找文本位置

        参数:
            target_pos: 目标的位置 (x, y)
            text: 要显示的文本
            frame_shape: 帧的形状
            existing_rects: 已存在的文本矩形列表

        返回:
            tuple: (text_x, text_y) 文本位置，如果找不到则返回None
        """
        text_width, text_height = self._calculate_text_size(text)
        target_x, target_y = target_pos

        # 螺旋搜索参数
        max_radius = min(100, min(frame_shape[0], frame_shape[1]) // 4)
        angle_step = 30  # 角度步长（度）
        radius_step = 10  # 半径步长

        for radius in range(self.text_offset_radius, max_radius, radius_step):
            for angle in range(0, 360, angle_step):
                # 计算螺旋位置
                rad = math.radians(angle)
                text_x = int(target_x + radius * math.cos(rad))
                text_y = int(target_y + radius * math.sin(rad))

                # 检查边界
                if (text_x >= 0 and text_y >= 0 and
                    text_x + text_width < frame_shape[1] and
                    text_y + text_height < frame_shape[0]):

                    # 检查重叠
                    new_rect = (text_x, text_y, text_width, text_height)
                    if not self._check_text_collision(new_rect, existing_rects):
                        return text_x, text_y

        return None

    def _calculate_dynamic_text_size(self, target_count):
        """
        根据目标数量动态计算文本大小

        参数:
            target_count: 当前帧中的目标数量

        返回:
            float: 动态调整后的文本大小
        """
        if not self.dynamic_text_size:
            return self.id_text_size

        # 根据目标密度调整文本大小
        if target_count <= 5:
            return self.max_text_size
        elif target_count <= 15:
            return self.id_text_size
        elif target_count <= 25:
            return max(self.min_text_size + 0.2, self.min_text_size)
        else:
            return self.min_text_size

    def _draw_text_with_background(self, frame, text, position, font_scale, color, thickness=2):
        """
        绘制带背景的文本以提高可读性

        参数:
            frame: 要绘制的帧
            text: 文本内容
            position: 文本位置 (x, y)
            font_scale: 字体大小
            color: 文本颜色
            thickness: 文本粗细
        """
        x, y = position

        if self.text_background:
            # 计算文本尺寸
            (text_width, text_height), baseline = cv2.getTextSize(
                text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)

            # 绘制半透明背景
            padding = 2
            bg_x1 = x - padding
            bg_y1 = y - text_height - padding
            bg_x2 = x + text_width + padding
            bg_y2 = y + baseline + padding

            # 创建背景覆盖层
            overlay = frame.copy()
            cv2.rectangle(overlay, (bg_x1, bg_y1), (bg_x2, bg_y2), (255, 255, 255), -1)
            cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

            # 绘制边框
            cv2.rectangle(frame, (bg_x1, bg_y1), (bg_x2, bg_y2), (200, 200, 200), 1)

        # 绘制文本
        cv2.putText(frame, text, position, cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness)

    def _get_color_by_id(self, id_str):
        if id_str not in self.target_colors:
            color = (
                np.random.randint(0, 255),
                np.random.randint(0, 255),
                np.random.randint(50, 200)
            )
            self.target_colors[id_str] = color
        return self.target_colors[id_str]

    def _get_matching_status_color(self, point, region_key):
        """
        根据匹配状态获取颜色

        参数:
            point: 轨迹点对象
            region_key: 区域键（'left'或'right'）

        返回:
            tuple: BGR颜色值
        """
        # 只对左侧输入目标应用匹配状态颜色
        if region_key == 'left' and self.show_matching_status:
            # 检查目标是否有is_matched属性
            if hasattr(point, 'is_matched'):
                if point.is_matched:
                    return (0, 255, 0)  # 绿色 - 已匹配
                else:
                    return (0, 0, 255)  # 红色 - 未匹配
            else:
                # 如果没有is_matched属性，默认为未匹配（红色）
                return (0, 0, 255)

        # 对于右侧目标或未启用匹配状态显示时，使用原有颜色逻辑
        return self._get_color_by_id(point.get_id_str())

    def _convert_to_canvas_coords(self, mercator_coords, region_key):
        # 使用缓存减少重复计算
        cache_key = (mercator_coords, region_key)
        if hasattr(self, '_coords_cache') and cache_key in self._coords_cache:
            return self._coords_cache[cache_key]

        x, y = mercator_coords
        dx = x - self.base_point_mercator[0]
        dy = y - self.base_point_mercator[1]

        # 在分离窗口模式下，左右区域的坐标系需要调整
        # 左区域的x坐标从0开始，右区域的x坐标也从0开始（相对于各自的窗口）
        if region_key == 'left':
            # 左窗口坐标系
            canvas_x = int(self.canvas_size[0] // 4 + dx * self.scale_factor)  # 窗口宽度的一半
            canvas_y = int(self.canvas_size[1] / 2 - dy * self.scale_factor)   # Y是相对于画布高度中心
        else:
            # 右窗口坐标系
            canvas_x = int(self.canvas_size[0] // 4 + dx * self.scale_factor)  # 窗口宽度的一半
            canvas_y = int(self.canvas_size[1] / 2 - dy * self.scale_factor)   # Y是相对于画布高度中心

        # 存入缓存
        if not hasattr(self, '_coords_cache'):
            self._coords_cache = {}
        self._coords_cache[cache_key] = (canvas_x, canvas_y)

        return (canvas_x, canvas_y)

    def draw_lane_lines(self, frame, region_key):
        # 在分离窗口模式下，不再需要检查区域边界
        if not self.lane_line_list:
            no_lanes_x = int(frame.shape[1]/2 - 100)  # 窗口中心
            no_lanes_y = int(frame.shape[0]/2)        # 窗口中心
            cv2.putText(frame, "no lane lines", (no_lanes_x, no_lanes_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 1)
            return

        # 使用预处理的缓存数据
        if region_key in self._lane_lines_cache:
            for lane_id, valid_points in self._lane_lines_cache[region_key].items():
                # 过滤掉不在当前帧内的点
                frame_points = []
                for point in valid_points:
                    if (0 <= point[0] < frame.shape[1] and
                        0 <= point[1] < frame.shape[0]):
                        frame_points.append(point)

                # 绘制车道线
                for i, point in enumerate(frame_points):
                    cv2.circle(frame, point, 1, (0, 255, 0), -1)
                    if i > 0:
                        cv2.line(frame, frame_points[i-1], point, (0, 255, 0), self.line_width)

                # 添加车道线标签
                if frame_points:
                    cv2.putText(frame, f"lane{lane_id}",
                               (frame_points[0][0], frame_points[0][1] - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

    def update_history_tracks(self, track_points, region_key, frame_shape=None):
        history_dict = self.history_tracks_left if region_key == 'left' else self.history_tracks_right

        # 创建临时字典，存储每个ID的最新坐标
        temp_coords = {}

        # 第一遍循环：收集所有点的坐标
        for point in track_points:
            try:
                id_str = point.get_id_str()
                mercator_coords = lonlat_to_mercator(point.longitude_update, point.latitude_update)
                if mercator_coords == (0, 0):
                    continue

                canvas_coords = self._convert_to_canvas_coords(mercator_coords, region_key)

                # 检查坐标是否在有效范围内
                if frame_shape is not None:
                    if not (0 <= canvas_coords[0] < frame_shape[1] and
                            0 <= canvas_coords[1] < frame_shape[0]):
                        continue

                # 存储坐标
                temp_coords[id_str] = canvas_coords
            except Exception:
                continue

        # 第二遍循环：更新历史轨迹
        for id_str, canvas_coords in temp_coords.items():
            if id_str not in history_dict:
                history_dict[id_str] = []

            history_dict[id_str].append(canvas_coords)
            if len(history_dict[id_str]) > self.max_history_length:
                history_dict[id_str] = history_dict[id_str][-self.max_history_length:]

    def draw_targets(self, frame, track_points, region_key):
        target_info_list = []

        if not track_points:
            no_points_x = int(frame.shape[1]/2 - 100)  # 窗口中心
            no_points_y = int(frame.shape[0]/2 + 30)   # 窗口中心稍下
            cv2.putText(frame, "no points", (no_points_x, no_points_y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 1)
            return target_info_list

        # 轨迹历史功能已禁用

        # 预处理：提前计算所有目标的坐标和颜色
        target_data = []
        for point in track_points:
            try:
                id_str = point.get_id_str()
                # 根据区域和匹配状态显示模式选择颜色
                color = self._get_matching_status_color(point, region_key)
                mercator_coords = lonlat_to_mercator(point.longitude_update, point.latitude_update)
                if mercator_coords == (0, 0):
                    continue

                canvas_coords = self._convert_to_canvas_coords(mercator_coords, region_key)

                # 检查坐标是否在当前帧内
                if not (0 <= canvas_coords[0] < frame.shape[1] and
                        0 <= canvas_coords[1] < frame.shape[0]):
                    continue

                # 收集目标数据
                target_data.append({
                    'point': point,
                    'id_str': id_str,
                    'color': color,
                    'canvas_coords': canvas_coords
                })

            except Exception:
                continue

        # 根据dynamic_text_position设置决定是否使用动态文本大小和碰撞检测
        if self.dynamic_text_position:
            # 计算动态文本大小
            dynamic_text_size = self._calculate_dynamic_text_size(len(target_data))
            original_text_size = self.id_text_size
            self.id_text_size = dynamic_text_size

            # 用于跟踪已绘制的文本矩形，避免重叠
            existing_text_rects = []
        else:
            # 使用原始文本大小，不进行动态调整
            original_text_size = self.id_text_size
            existing_text_rects = []

        # 绘制目标
        for data in target_data:
            point = data['point']
            id_str = data['id_str']
            color = data['color']
            canvas_coords = data['canvas_coords']

            use_car_image_for_this_target = point.is_pass_ovd == 1 or self.use_car_image
            if use_car_image_for_this_target and hasattr(self, 'resized_car') and self.resized_car is not None:
                x, y = canvas_coords
                x1, y1 = x - self.car_size // 2, y - self.car_size // 2
                x2, y2 = x1 + self.car_size, y1 + self.car_size

                # 检查车辆图像是否在帧内
                if (0 <= x1 and x2 < frame.shape[1] and
                    0 <= y1 and y2 < frame.shape[0]):
                    if hasattr(self, 'resized_alpha') and self.resized_alpha is not None:
                        colored_car = self.resized_car.copy()
                        colored_car[:, :, 0], colored_car[:, :, 1], colored_car[:, :, 2] = color[0], color[1], color[2]
                        roi = frame[y1:y2, x1:x2]
                        for c_idx in range(3):
                            roi[:, :, c_idx] = (self.resized_alpha / 255.0) * colored_car[:, :, c_idx] + \
                                          (1.0 - self.resized_alpha / 255.0) * roi[:, :, c_idx]
                        frame[y1:y2, x1:x2] = roi
                    else:
                        colored_car = self.resized_car.copy()
                        colored_car[:, :, 0], colored_car[:, :, 1], colored_car[:, :, 2] = color[0], color[1], color[2]
                        frame[y1:y2, x1:x2] = colored_car
                else:
                    cv2.circle(frame, canvas_coords, 4, color, -1)
            else:
                cv2.circle(frame, canvas_coords, 4, color, -1)

            # 如果设置了在目标旁边显示ID，根据dynamic_text_position选择布局方式
            if self.show_id_at_target:
                # id_text = f"ID:{point.get_data_id()}"
                id_text = f"{point.get_draw_str()}"

                if self.dynamic_text_position and self.text_collision_detection:
                    # 使用智能文本布局算法
                    optimal_pos = self._find_optimal_text_position(
                        canvas_coords, id_text, frame.shape, existing_text_rects)

                    if optimal_pos is not None:
                        text_x, text_y = optimal_pos
                        # 绘制带背景的文本（如果启用）
                        if self.text_background:
                            self._draw_text_with_background(
                                frame, id_text, (text_x, text_y), self.id_text_size, color)
                        else:
                            cv2.putText(frame, id_text, (text_x, text_y),
                                       cv2.FONT_HERSHEY_SIMPLEX, self.id_text_size, color, 2)

                        # 记录文本矩形以避免后续重叠
                        text_width, text_height = self._calculate_text_size(id_text)
                        existing_text_rects.append((text_x, text_y, text_width, text_height))
                    # 如果找不到合适位置，则不显示文本（避免重叠）
                else:
                    # 使用原始的简单固定偏移布局
                    text_x = canvas_coords[0] + 20  # 固定右侧偏移20像素
                    text_y = canvas_coords[1] + 5   # 固定下方偏移5像素

                    # 检查是否在帧边界内
                    if (text_x >= 0 and text_y >= 0 and
                        text_x < frame.shape[1] and text_y < frame.shape[0]):
                        if self.dynamic_text_position and self.text_background:
                            # 如果启用了动态文本位置但关闭了碰撞检测，仍可使用背景
                            self._draw_text_with_background(
                                frame, id_text, (text_x, text_y), self.id_text_size, color)
                        else:
                            # 使用最简单的文本绘制方式（完全兼容原版本）
                            cv2.putText(frame, id_text, (text_x, text_y),
                                       cv2.FONT_HERSHEY_SIMPLEX, self.id_text_size, color, 2)

            if region_key == 'right':
                target_info = {'id': point.get_data_id(), 'is_pass_ovd': point.is_pass_ovd, 'color': color}
                target_info_list.append(target_info)

        # 恢复原始文本大小（仅在启用动态文本位置时需要恢复）
        if self.dynamic_text_position:
            self.id_text_size = original_text_size

        # 轨迹历史绘制已禁用

        return target_info_list

    def _draw_target_info_panel(self, frame, target_info_list):
        # 如果设置了在目标旁边显示ID，则不显示右上角的面板
        if not target_info_list or self.show_id_at_target: return

        panel_x = self.right_region[0] + (self.right_region[2]-self.right_region[0]) - 300 # Position relative to right region
        panel_y = 30
        line_height = 30
        panel_height = len(target_info_list) * line_height + 20
        panel_width = 280
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x - 10, panel_y - 10),
                     (panel_x + panel_width, panel_y + panel_height),
                     (240, 240, 240), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        cv2.putText(frame, "target:", (panel_x, panel_y + 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        for i, info in enumerate(target_info_list):
            y_pos = panel_y + 40 + i * line_height
            text = f"ID: {info['id']} ovd: {info['is_pass_ovd']}"
            cv2.putText(frame, text, (panel_x, y_pos), cv2.FONT_HERSHEY_SIMPLEX, self.id_text_size, info['color'], 2)

    def _draw_statistics_panel(self, frame):
        """
        在右侧区域绘制统计信息面板
        """
        if self.relay_statistics is None:
            return

        panel_x = 10
        panel_y = 100
        panel_width = 300
        panel_height = 100

        # 绘制统计面板
        self.relay_statistics.draw_statistics_panel(frame, panel_x, panel_y, panel_width, panel_height)

    def draw_info(self, frame, frame_idx, total_frames):
        # This info is for the whole canvas, top-left of the entire window
        panel_width, panel_height = 220, 100 # Increased height for matching status indicator
        overlay = frame.copy()
        cv2.rectangle(overlay, (5, 5), (5 + panel_width, 5 + panel_height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        cv2.putText(frame, f"Frame: {frame_idx}/{total_frames}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # 添加匹配状态显示指示器
        status_text = "Matching Status: ON" if self.show_matching_status else "Matching Status: OFF"
        status_color = (0, 255, 0) if self.show_matching_status else (128, 128, 128)
        cv2.putText(frame, status_text, (10, 55),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, status_color, 2)

        # 如果启用了匹配状态显示，添加颜色说明
        if self.show_matching_status:
            cv2.putText(frame, "Green=Matched, Red=Unmatched", (10, 75),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # General Scale bar (top-left of entire window).
        # This scale bar refers to the self.scale_factor, which is global.
        # If region-specific scales were introduced, this would need adjustment.
        scale_length_pixels = 100
        real_distance_meters = scale_length_pixels / self.scale_factor
        cv2.line(frame, (10, 60), (10 + scale_length_pixels, 60), (255, 255, 255), self.line_width)
        cv2.putText(frame, f"{real_distance_meters:.1f}m", (10, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    def draw_device(self, frame, region_key):
        # 使用预处理的缓存数据
        if region_key in self._device_coords_cache:
            for canvas_coords in self._device_coords_cache[region_key]:
                # 检查坐标是否在当前帧内
                if (0 <= canvas_coords[0] < frame.shape[1] and
                    0 <= canvas_coords[1] < frame.shape[0]):
                    cv2.circle(frame, canvas_coords, 5, (0, 0, 255), -1)

    def draw_distance_circles(self, frame, region_key):
        # 如果没有缓存，创建距离圆缓存
        if not hasattr(self, '_distance_circles_cache'):
            self._distance_circles_cache = {'left': [], 'right': []}

        # 如果当前区域的缓存为空，计算并缓存
        if not self._distance_circles_cache[region_key]:
            circle_interval = self.circle_interval
            base_canvas_coords = self._convert_to_canvas_coords(self.base_point_mercator, region_key)

            # 使用显示范围来确定圆的数量
            max_distance_meters = self.display_range / 2  # 显示范围的一半
            num_circles = int(max_distance_meters / circle_interval)

            circles = []
            for i in range(1, num_circles + 1):
                distance_m = i * circle_interval
                radius_pixels = int(distance_m * self.scale_factor)
                label_x = base_canvas_coords[0] + int(radius_pixels * 0.7071)
                label_y = base_canvas_coords[1] - int(radius_pixels * 0.7071)

                circles.append({
                    'center': base_canvas_coords,
                    'radius': radius_pixels,
                    'distance': distance_m,
                    'label_pos': (label_x, label_y)
                })

            self._distance_circles_cache[region_key] = circles

        # 使用缓存绘制距离圆
        for circle_info in self._distance_circles_cache[region_key]:
            cv2.circle(frame, circle_info['center'], circle_info['radius'], (200, 200, 200), self.line_width)

            # 检查标签坐标是否在当前帧内
            label_x, label_y = circle_info['label_pos']
            if (0 <= label_x < frame.shape[1] and
                0 <= label_y < frame.shape[0]):
                cv2.putText(frame, f"{circle_info['distance']}m", (label_x, label_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (100, 100, 100), 2)

    def create_visualization(self, output_path=None, show_window=False):
        if output_path:
            try:
                fourcc = cv2.VideoWriter_fourcc(*'XVID')
                out = cv2.VideoWriter(output_path, fourcc, 10.0, self.canvas_size)
                if not out.isOpened(): print(f"Error: Cannot create video file {output_path}"); return
            except Exception as e: print(f"Error creating video writer: {e}"); return

        # 计算单个窗口的大小
        single_window_width = self.canvas_size[0] // 2
        single_window_height = self.canvas_size[1]

        if show_window:
            try:
                # 创建两个独立的窗口
                cv2.namedWindow("Left Region - Input", cv2.WINDOW_NORMAL)
                cv2.namedWindow("Right Region - Output", cv2.WINDOW_NORMAL)
                cv2.resizeWindow("Left Region - Input", 960, 1080)
                cv2.resizeWindow("Right Region - Output", 960, 1080)
                # 将两个窗口并排放置
                cv2.moveWindow("Left Region - Input", 0, 0)
                cv2.moveWindow("Right Region - Output", 960, 0)
            except Exception as e: print(f"Error creating windows: {e}"); show_window = False

        total_frames = max(len(self.track_output_frames), len(self.track_input_frames))
        if total_frames == 0: print("No track data to visualize."); return

        print(f"Starting to process {total_frames} frames...")
        is_paused = False; frame_idx = 0

        # 预先创建控制信息文本
        controls_text = "Space=Pause/Play | A/D=Back/Forward | C=Car | I=ID mode | M=Matching | T=Text collision | B=Background | +/-=Text size | P=Dynamic position | ESC/Q=Quit"

        # 预先创建静态元素的帧
        static_frame_left = np.ones((single_window_height, single_window_width, 3), dtype=np.uint8) * 255
        static_frame_right = np.ones((single_window_height, single_window_width, 3), dtype=np.uint8) * 255

        # 绘制静态元素
        self.draw_distance_circles(static_frame_left, 'left')
        self.draw_lane_lines(static_frame_left, 'left')
        self.draw_device(static_frame_left, 'left')

        self.draw_distance_circles(static_frame_right, 'right')
        self.draw_lane_lines(static_frame_right, 'right')
        self.draw_device(static_frame_right, 'right')

        while frame_idx < total_frames:
            output_track_points = self.track_output_frames[frame_idx] if frame_idx < len(self.track_output_frames) else []
            input_track_points = self.track_input_frames[frame_idx] if frame_idx < len(self.track_input_frames) else []

            # 复制静态帧作为当前帧的基础
            frame_left = static_frame_left.copy()
            frame_right = static_frame_right.copy()

            # 如果需要输出视频，创建完整帧
            if output_path:
                frame_full = np.ones((self.canvas_size[1], self.canvas_size[0], 3), dtype=np.uint8) * 255

            try:
                # 只绘制动态元素
                self.draw_targets(frame_left, input_track_points, 'left')
                target_info_list_right = self.draw_targets(frame_right, output_track_points, 'right')
                if target_info_list_right:
                    self._draw_target_info_panel(frame_right, target_info_list_right)

                # 在统计模式下才绘制统计信息面板
                if self.relay_statistics is not None:
                    self._draw_statistics_panel(frame_right)

                # 在两个窗口中都显示帧信息
                self.draw_info(frame_left, frame_idx, total_frames)
                self.draw_info(frame_right, frame_idx, total_frames)

                # 更新状态文本
                status_text = "Paused (Space to Play)" if is_paused else "Playing (Space to Pause)"

                # 左窗口控制信息
                cv2.putText(frame_left, status_text, (10, single_window_height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
                cv2.putText(frame_left, f"Controls: {controls_text}", (10, single_window_height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)

                # 右窗口控制信息
                cv2.putText(frame_right, status_text, (10, single_window_height - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
                cv2.putText(frame_right, f"Controls: {controls_text}", (10, single_window_height - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)

                if frame_idx % 10 == 0 and not is_paused:
                    print(f"Progress: {frame_idx}/{total_frames} ({frame_idx/total_frames*100:.1f}%)")

                if show_window:
                    # 显示两个窗口
                    cv2.imshow("Left Region - Input", frame_left)
                    cv2.imshow("Right Region - Output", frame_right)

                    # 等待按键，两个窗口共享控制
                    wait_time = 0 if is_paused else 100
                    key = cv2.waitKey(wait_time)
                    if key != -1 and key & 0xFF != 255:
                        print(f"Key pressed: {key}")
                    if key == 27 or key == ord('q'):
                        break
                    elif key == 32 or key == ord(' '):
                        is_paused = not is_paused
                        print("Playback status switched to:", "Paused" if is_paused else "Playing")
                    elif key in [81, 2424832, 65361, ord('a'), ord('A'), 2]:
                        frame_idx = max(0, frame_idx - 10)
                        print(f"Going back to frame {frame_idx}")
                        continue
                    elif key in [83, 2555904, 65363, ord('d'), ord('D'), 3]:
                        frame_idx = min(total_frames - 1, frame_idx + 10)
                        print(f"Going forward to frame {frame_idx}")
                        continue
                    elif key == ord('c') or key == ord('C'):
                        self.use_car_image = not self.use_car_image
                        print("Display mode switched to:", "Car Images" if self.use_car_image else "Circles (OVD vehicles still shown as car images)")
                    elif key == ord('i') or key == ord('I'):
                        mode = self.toggle_id_display_mode()
                        print("ID display mode switched to:", mode)
                    elif key == ord('m') or key == ord('M'):
                        status = self.toggle_matching_status_display()
                        print("Matching status display:", status)
                        if status == "Enabled":
                            print("  - Input targets: Green=Matched, Red=Unmatched")
                            print("  - Output targets: Original colors")
                        else:
                            print("  - All targets: Original ID-based colors")
                    elif key == ord('t') or key == ord('T'):
                        self.text_collision_detection = not self.text_collision_detection
                        print("Text collision detection:", "Enabled" if self.text_collision_detection else "Disabled")
                    elif key == ord('b') or key == ord('B'):
                        self.text_background = not self.text_background
                        print("Text background:", "Enabled" if self.text_background else "Disabled")
                    elif key == ord('+') or key == ord('='):
                        self.set_text_size(self.id_text_size + 0.1)
                        print(f"Text size increased to: {self.id_text_size:.1f}")
                    elif key == ord('-') or key == ord('_'):
                        self.set_text_size(self.id_text_size - 0.1)
                        print(f"Text size decreased to: {self.id_text_size:.1f}")
                    elif key == ord('r') or key == ord('R'):
                        if self.dynamic_text_position:
                            self.dynamic_text_size = not self.dynamic_text_size
                            print("Dynamic text sizing:", "Enabled" if self.dynamic_text_size else "Disabled")
                        else:
                            print("Dynamic text sizing is disabled (requires dynamic text position to be enabled)")
                    elif key == ord('p') or key == ord('P'):
                        status = self.toggle_dynamic_text_position()
                        print(f"Dynamic text position: {status}")
                        if status == "Enabled":
                            print("  - Smart text layout algorithm enabled")
                            print("  - Text collision detection enabled")
                            print("  - Dynamic text sizing enabled")
                        else:
                            print("  - Using simple fixed offset layout")
                            print("  - Text collision detection disabled")
                            print("  - Dynamic text sizing disabled")

                # 如果需要输出视频，将左右帧合并为完整帧
                if output_path and out.isOpened():
                    frame_full[:, 0:single_window_width, :] = frame_left
                    frame_full[:, single_window_width:, :] = frame_right
                    cv2.line(frame_full, (single_window_width, 0), (single_window_width, self.canvas_size[1]), (0,0,0), 2)
                    out.write(frame_full)
            except Exception as e:
                print(f"Error processing frame {frame_idx}: {e}")
                import traceback; traceback.print_exc()

            if not is_paused:
                frame_idx += 1

        if output_path and out.isOpened():
            try:
                out.release()
                print(f"Video saved to: {output_path}")
            except Exception as e:
                print(f"Error closing video writer: {e}")
        if show_window:
            try:
                cv2.destroyAllWindows()
            except Exception as e:
                print(f"Error closing windows: {e}")
        print("Visualization completed!")