import cv2
import numpy as np
import argparse
import os
import time
import math
import matplotlib.pyplot as plt
import pandas as pd
from utils import calculate_distance

# 添加轨迹点类定义
class TrackPoint:
    """
    轨迹点类，封装轨迹点的所有属性
    """
    def __init__(self, splice_id=0, x=0, y=0, bx=0, by=0, target_id=0, device_id=0,
                 status=0, timestamp_ms=0, time_ms=0, longitude=0, latitude=0, longitude_update=0, latitude_update=0, is_pass_ovd=0):
        # 拼接ID
        self.splice_id = splice_id
        # 坐标x
        self.x = x
        # 坐标y
        self.y = y
        # 原始坐标x
        self.bx = bx
        # 原始坐标y
        self.by = by
        # 目标ID
        self.target_id = target_id
        # 设备ID
        self.device_id = device_id
        # 状态
        self.status = status
        # 时间戳(ms)
        self.timestamp_ms = timestamp_ms
        # 时间(ms)
        self.time_ms = time_ms
        # 经度
        self.longitude = longitude
        # 纬度
        self.latitude = latitude
        # 经度更新
        self.longitude_update = longitude_update
        # 纬度更新
        self.latitude_update = latitude_update
        self.is_pass_ovd = is_pass_ovd

    def get_data_id(self):
        """获取数据ID：如果splice_id不为0则返回splice_id，否则返回target_id"""
        try:
            # 尝试将splice_id转换为整数进行比较
            if isinstance(self.splice_id, str):
                # 如果是字符串，检查是否为数字字符串
                if self.splice_id.isdigit() and int(self.splice_id) != 0:
                    return f'{self.splice_id}'
                elif not self.splice_id.isdigit():
                    # 如果不是数字字符串，直接返回
                    return f'{self.splice_id}'
                else:
                    return f'{self.target_id}'
            else:
                # 如果是数字类型
                return f'{self.splice_id}' if int(self.splice_id) != 0 else f'{self.target_id}'
        except (ValueError, TypeError):
            # 如果转换失败，直接返回splice_id
            return f'{self.splice_id}' if self.splice_id else f'{self.target_id}'

    def get_id_str(self):
        """获取完整ID字符串，用于显示和颜色区分"""
        return f'{self.splice_id}'


    def get_draw_str(self):
        """获取完整ID字符串，用于显示和颜色区分"""
        if int(self.splice_id) < 10000:
            return f'{self.target_id}_{self.device_id}'
        return f'{self.splice_id}_{self.target_id}_{self.device_id}'

    # 距离差
    @property
    def ds(self):
        ds = calculate_distance((self.longitude, self.latitude), (self.longitude_update, self.latitude_update))
        return ds
