import cv2
import numpy as np
import pandas as pd
import os
from datetime import datetime
from utils import calculate_distance

class RelayStatistics:
    """
    接力统计类，用于统计接力目标的准确率和召回率
    """
    def __init__(self, track_input_frames, track_output_frames, distance_threshold=800.0, max_targets=100, recall_distance_threshold=100.0, min_device_count=2):
        """
        初始化接力统计类

        参数:
            track_input_frames: 输入轨迹点帧列表
            track_output_frames: 输出轨迹点帧列表
            distance_threshold: 前进距离阈值（米），默认为800米
            max_targets: 用于统计的最大融合目标数量，默认为100
            recall_distance_threshold: 召回率计算中的距离阈值（米），默认为100米
            min_device_count: 有效目标的最小设备数量要求，默认为2
        """
        self.track_input_frames = track_input_frames
        self.track_output_frames = track_output_frames
        self.distance_threshold = distance_threshold
        self.max_targets = max_targets
        self.recall_distance_threshold = recall_distance_threshold
        self.min_device_count = min_device_count
        self.is_filtered = max_targets > 0  # 标记是否进行了目标筛选

        # 统计结果
        self.accuracy_results = {
            'total_targets': 0,
            'valid_targets': 0,
            'accuracy': 0.0
        }

        self.recall_results = {
            'total_input_targets': 0,
            'matched_targets': 0,
            'recall': 0.0
        }

        # 设备与目标的映射关系
        self.device_target_map = {}

        # 详细的目标统计数据
        self.target_details = []

        # 筛选后的融合目标ID列表
        self.filtered_fusion_ids = []

        # 设备1的目标前进距离信息
        self.device1_targets_distance = {}

        # 初始化时计算统计结果
        self._calculate_statistics()

    def _calculate_statistics(self):
        """
        计算统计结果
        """
        self._calculate_accuracy()
        self._calculate_recall()

    def _calculate_accuracy(self):
        """
        计算准确率：统计每个融合目标的前进距离是否大于阈值且经过不少于两个设备的比例
        如果设置了max_targets，则只统计中间的max_targets个融合目标
        """
        # 清空目标详细信息列表
        self.target_details = []
        self.filtered_fusion_ids = []

        # 按融合ID分组的目标字典
        targets_by_fusion_id = {}

        # 遍历所有输出帧，收集每个融合ID的所有目标
        for frame_idx, frame in enumerate(self.track_output_frames):
            for target in frame:
                # 使用融合ID作为键
                fusion_id = str(target.splice_id)

                # 跳过splice_id为0的目标
                if target.splice_id == 0:
                    continue

                # 如果这个融合ID还没有记录，创建一个新的记录
                if fusion_id not in targets_by_fusion_id:
                    targets_by_fusion_id[fusion_id] = {
                        'fusion_id': fusion_id,
                        'device_id': target.device_id,
                        'target_ids': set([target.target_id]),  # 使用集合存储所有参与接力的目标ID
                        'device_ids': set([target.device_id]),  # 记录经过的设备ID集合
                        'first_timestamp': target.timestamp_ms,
                        'last_timestamp': target.timestamp_ms,
                        'first_position': (target.longitude, target.latitude),  # 记录第一个位置
                        'last_position': (target.longitude, target.latitude),   # 初始时，最后位置与第一个位置相同
                        'frames': [frame_idx],
                        'frame_positions': {frame_idx: (target.longitude, target.latitude)},  # 记录每一帧的位置
                        'is_valid': False  # 初始设为False，后面会重新计算
                    }
                else:
                    # 更新现有记录
                    record = targets_by_fusion_id[fusion_id]
                    record['target_ids'].add(target.target_id)
                    record['device_ids'].add(target.device_id)  # 添加设备ID到集合中

                    # 更新时间戳
                    if target.timestamp_ms < record['first_timestamp']:
                        record['first_timestamp'] = target.timestamp_ms
                        record['first_position'] = (target.longitude, target.latitude)

                    if target.timestamp_ms > record['last_timestamp']:
                        record['last_timestamp'] = target.timestamp_ms
                        record['last_position'] = (target.longitude, target.latitude)

                    # 记录当前帧的位置
                    record['frames'].append(frame_idx)
                    record['frame_positions'][frame_idx] = (target.longitude, target.latitude)

        # 获取所有融合ID并按照融合ID排序
        all_fusion_ids = sorted([int(fusion_id) for fusion_id in targets_by_fusion_id.keys() if fusion_id.isdigit()])

        # 如果设置了max_targets且有足够的融合目标，则只选择中间的max_targets个
        if self.is_filtered and self.max_targets > 0 and len(all_fusion_ids) > self.max_targets:
            # 计算中间部分的起始索引
            start_idx = (len(all_fusion_ids) - self.max_targets) // 2
            # 选择中间的max_targets个融合ID
            selected_fusion_ids = all_fusion_ids[start_idx:start_idx + self.max_targets]
            # 将选中的融合ID转换为字符串并保存
            self.filtered_fusion_ids = [str(fusion_id) for fusion_id in selected_fusion_ids]

            # 只保留选中的融合目标
            filtered_targets = {fusion_id: targets_by_fusion_id[fusion_id] for fusion_id in self.filtered_fusion_ids if fusion_id in targets_by_fusion_id}
            targets_by_fusion_id = filtered_targets
        else:
            # 如果不需要筛选或者目标数量不足，则使用所有融合ID
            self.filtered_fusion_ids = [str(fusion_id) for fusion_id in all_fusion_ids]

        # 统计融合目标数量和有效融合目标数量
        total_fusion_targets = len(targets_by_fusion_id)
        valid_fusion_targets = 0

        # 将分组后的数据转换为列表
        for fusion_id, record in targets_by_fusion_id.items():
            # 将目标ID集合转换为逗号分隔的字符串
            target_ids_str = ','.join(map(str, record['target_ids']))

            # 将设备ID集合转换为逗号分隔的字符串
            device_ids_str = ','.join(map(str, sorted(record['device_ids'])))

            # 计算经过的设备数量
            device_count = len(record['device_ids'])

            # 计算时间差（毫秒）
            time_duration_ms = record['last_timestamp'] - record['first_timestamp']

            # 计算第一个点和最后一个点之间的距离
            distance = calculate_distance(record['first_position'], record['last_position'])

            # 判断是否为有效目标：距离大于阈值 且 经过不少于指定数量的设备
            is_valid = distance > self.distance_threshold and device_count >= self.min_device_count
            if is_valid:
                valid_fusion_targets += 1

            # 创建详细信息记录
            target_detail = {
                'fusion_id': record['fusion_id'],
                'device_id': record['device_id'],
                'target_ids': target_ids_str,
                'device_ids': device_ids_str,  # 添加经过的设备ID列表
                'device_count': device_count,  # 添加经过的设备数量
                'first_timestamp_ms': record['first_timestamp'],
                'last_timestamp_ms': record['last_timestamp'],
                'time_duration_ms': time_duration_ms,
                'first_longitude': record['first_position'][0],
                'first_latitude': record['first_position'][1],
                'last_longitude': record['last_position'][0],
                'last_latitude': record['last_position'][1],
                'total_distance': distance,  # 使用第一个点和最后一个点之间的距离
                'frame_count': len(record['frames']),
                'is_valid': is_valid,
                'threshold': self.distance_threshold
            }

            self.target_details.append(target_detail)

        # 计算准确率（使用融合目标数量作为分母）
        accuracy = valid_fusion_targets / total_fusion_targets if total_fusion_targets > 0 else 0.0

        # 更新统计结果
        self.accuracy_results = {
            'total_targets': total_fusion_targets,  # 现在是融合目标的总数
            'valid_targets': valid_fusion_targets,  # 有效的融合目标数
            'accuracy': accuracy
        }

    def _calculate_recall(self):
        """
        计算召回率：统计设备1的目标并且前进距离大于100m的目标中，有多少参与了拼接目标的形成

        召回率计算逻辑：
        1. 以设备1的目标并且前进距离大于100m的目标数量为分母
        2. 成功匹配到拼接目标的输入目标数量为分子
        3. 输入目标如果参与了某个拼接目标的形成，则计为匹配成功

        如果已经筛选了融合目标，则只统计与筛选后的融合目标相关的输入目标
        """
        # 清空设备1的目标前进距离信息
        self.device1_targets_distance = {}

        # 统计所有唯一的输入目标，并计算设备1目标的前进距离
        input_targets = {}
        device1_targets_positions = {}  # 记录设备1每个目标的首次和最后位置

        # 第一遍遍历：收集设备1目标的位置信息
        for frame_idx, frame in enumerate(self.track_input_frames):
            for target in frame:
                # 只关注设备1的目标
                if target.device_id == 1:
                    key = (target.device_id, target.target_id)
                    position = (target.longitude, target.latitude)

                    if key not in device1_targets_positions:
                        device1_targets_positions[key] = {
                            'first_position': position,
                            'last_position': position,
                            'first_timestamp': target.timestamp_ms,
                            'last_timestamp': target.timestamp_ms
                        }
                    else:
                        # 更新最后位置
                        if target.timestamp_ms > device1_targets_positions[key]['last_timestamp']:
                            device1_targets_positions[key]['last_position'] = position
                            device1_targets_positions[key]['last_timestamp'] = target.timestamp_ms
                        # 更新首次位置
                        if target.timestamp_ms < device1_targets_positions[key]['first_timestamp']:
                            device1_targets_positions[key]['first_position'] = position
                            device1_targets_positions[key]['first_timestamp'] = target.timestamp_ms

        # 计算设备1目标的前进距离
        for key, positions in device1_targets_positions.items():
            distance = calculate_distance(positions['first_position'], positions['last_position'])
            self.device1_targets_distance[key] = distance

        # 第二遍遍历：收集所有输入目标
        for frame_idx, frame in enumerate(self.track_input_frames):
            for target in frame:
                key = (target.device_id, target.target_id)
                if key not in input_targets:
                    input_targets[key] = {
                        'device_id': target.device_id,
                        'target_id': target.target_id,
                        'matched': False,  # 初始化为未匹配
                        'related_fusion_ids': set(),  # 记录与该输入目标相关的融合ID
                        'distance': self.device1_targets_distance.get(key, 0)  # 记录前进距离
                    }

        # 收集所有输出拼接目标及其包含的原始目标ID
        fusion_targets = {}
        for frame_idx, frame in enumerate(self.track_output_frames):
            for target in frame:
                # 只处理有效的拼接目标
                if target.splice_id == 0:
                    continue

                fusion_id = str(target.splice_id)

                # 如果已经筛选了融合目标，则只处理筛选后的融合目标
                if self.is_filtered and self.filtered_fusion_ids and fusion_id not in self.filtered_fusion_ids:
                    continue

                if fusion_id not in fusion_targets:
                    fusion_targets[fusion_id] = {
                        'device_id': target.device_id,
                        'target_ids': set([target.target_id])  # 使用集合存储所有参与接力的目标ID
                    }
                else:
                    # 更新现有记录，添加新的目标ID
                    fusion_targets[fusion_id]['target_ids'].add(target.target_id)

                # 记录输入目标与融合目标的关系
                key = (target.device_id, target.target_id)
                if key in input_targets:
                    input_targets[key]['related_fusion_ids'].add(fusion_id)

        # 检查每个输入目标是否参与了某个拼接目标的形成
        for key, target_info in input_targets.items():
            device_id, target_id = key[0], key[1]  # 正确解包元组

            # 检查该输入目标是否在任何拼接目标中
            for fusion_id, fusion_info in fusion_targets.items():
                # 如果设备ID匹配且目标ID在拼接目标的目标ID集合中
                if device_id == fusion_info['device_id'] and target_id in fusion_info['target_ids']:
                    target_info['matched'] = True
                    break

        # 如果已经筛选了融合目标，则只统计与筛选后的融合目标相关的输入目标
        if self.is_filtered and self.filtered_fusion_ids:
            # 筛选出与选定融合目标相关的输入目标
            related_input_targets = {
                key: info for key, info in input_targets.items()
                if any(fusion_id in self.filtered_fusion_ids for fusion_id in info['related_fusion_ids'])
            }

            # 如果没有相关的输入目标，则使用所有输入目标
            if related_input_targets:
                input_targets = related_input_targets

        # 筛选出设备1的目标并且前进距离大于阈值的目标
        device1_valid_targets = {
            key: info for key, info in input_targets.items()
            if key[0] == 1 and info['distance'] > self.recall_distance_threshold
        }

        # 计算匹配的目标数和总输入目标数
        total_device1_valid_targets = len(device1_valid_targets)
        matched_device1_valid_targets = sum(1 for target_info in device1_valid_targets.values() if target_info['matched'])

        # 计算召回率
        recall = matched_device1_valid_targets / total_device1_valid_targets if total_device1_valid_targets > 0 else 0.0

        # 更新统计结果
        self.recall_results = {
            'total_input_targets': total_device1_valid_targets,  # 设备1的目标并且前进距离大于阈值的目标数量
            'matched_targets': matched_device1_valid_targets,    # 匹配成功的目标数量
            'recall': recall
        }

    def get_accuracy_results(self):
        """
        获取准确率统计结果

        返回:
            dict: 包含准确率统计结果的字典
        """
        return self.accuracy_results

    def get_recall_results(self):
        """
        获取召回率统计结果

        返回:
            dict: 包含召回率统计结果的字典
        """
        return self.recall_results

    def get_statistics_summary(self):
        """
        获取统计结果摘要

        返回:
            str: 统计结果摘要字符串
        """
        accuracy = self.accuracy_results['accuracy'] * 100
        recall = self.recall_results['recall'] * 100

        summary = f"接力准确率: {accuracy:.2f}% ({self.accuracy_results['valid_targets']}/{self.accuracy_results['total_targets']})\n"
        summary += f"接力召回率: {recall:.2f}% ({self.recall_results['matched_targets']}/{self.recall_results['total_input_targets']})"

        return summary

    def save_statistics_to_excel(self, output_dir=None):
        """
        将统计结果保存到Excel文件

        参数:
            output_dir: 输出目录，如果为None则保存到当前目录

        返回:
            str: 保存的Excel文件路径
        """
        # 创建输出目录
        if output_dir is None:
            output_dir = '.'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成文件名，包含时间戳和阈值信息
        timestamp = ""
        threshold_str = str(int(self.distance_threshold))

        # 如果是筛选模式，在文件名中标注
        if self.is_filtered and self.max_targets > 0:
            excel_filename = f"relay_statistics_{threshold_str}m_filtered{self.max_targets}_{timestamp}.xlsx"
        else:
            excel_filename = f"relay_statistics_{threshold_str}m_{timestamp}.xlsx"

        excel_path = os.path.join(output_dir, excel_filename)

        # 将目标详细信息转换为DataFrame
        if self.target_details:
            # 创建Excel写入器，使用ExcelWriter可以将多个表写入同一个Excel文件
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                # 创建详细数据DataFrame
                df = pd.DataFrame(self.target_details)

                # 重新排序列，使重要信息在前面
                columns_order = [
                    'fusion_id', 'device_id', 'target_ids', 'device_ids', 'device_count',
                    'total_distance', 'is_valid', 'threshold',
                    'first_timestamp_ms', 'last_timestamp_ms', 'time_duration_ms',
                    'frame_count'
                ]

                # 确保所有列都存在
                for col in columns_order[:]:
                    if col not in df.columns:
                        columns_order.remove(col)

                # 添加其他可能存在的列
                for col in df.columns:
                    if col not in columns_order:
                        columns_order.append(col)

                # 重新排序
                df = df[columns_order]

                # 设置表名，如果是筛选模式，在表名中标注
                sheet_name = '融合目标详情'
                if self.is_filtered and self.max_targets > 0:
                    sheet_name = f'融合目标详情(筛选{self.max_targets}个)'

                # 将详细数据写入表
                df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 创建摘要数据
                summary_items = [
                    '融合目标总数', '有效融合目标数', '准确率',
                    f'设备1且前进距离>{self.recall_distance_threshold}m的目标数', '匹配成功的目标数', '召回率',
                    '平均首尾直线距离(m)', '平均持续时间(ms)', '平均帧数', '平均经过设备数量',
                    f'真正的接力目标数(≥{self.min_device_count}个设备)', '接力目标比例(%)',
                    '准确率距离阈值(m)', '召回率距离阈值(m)', '最小设备数量要求'
                ]

                # 如果是筛选模式，添加筛选说明
                if self.is_filtered and self.max_targets > 0:
                    summary_items.insert(0, '统计说明')

                summary_data = {
                    '统计项': summary_items,
                    '数值': []
                }

                # 添加筛选说明
                if self.is_filtered and self.max_targets > 0:
                    summary_data['数值'].append(f"仅统计中间{self.max_targets}个融合目标")

                # 计算设备相关统计
                if self.target_details:
                    avg_device_count = sum(detail['device_count'] for detail in self.target_details) / len(self.target_details)
                    relay_targets = sum(1 for detail in self.target_details if detail['device_count'] >= self.min_device_count)
                    relay_percentage = (relay_targets / len(self.target_details)) * 100
                else:
                    avg_device_count = 0
                    relay_targets = 0
                    relay_percentage = 0

                # 添加其他统计数据
                summary_data['数值'].extend([
                    self.accuracy_results['total_targets'],
                    self.accuracy_results['valid_targets'],
                    f"{self.accuracy_results['accuracy'] * 100:.2f}%",
                    self.recall_results['total_input_targets'],
                    self.recall_results['matched_targets'],
                    f"{self.recall_results['recall'] * 100:.2f}%",
                    f"{sum(detail['total_distance'] for detail in self.target_details) / len(self.target_details):.2f}" if self.target_details else "N/A",
                    f"{sum(detail['time_duration_ms'] for detail in self.target_details) / len(self.target_details):.2f}" if self.target_details else "N/A",
                    f"{sum(detail['frame_count'] for detail in self.target_details) / len(self.target_details):.2f}" if self.target_details else "N/A",
                    f"{avg_device_count:.2f}",
                    relay_targets,
                    f"{relay_percentage:.1f}%",
                    self.distance_threshold,
                    self.recall_distance_threshold,
                    self.min_device_count
                ])

                # 将摘要数据写入"统计摘要"表
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计摘要', index=False)

                # 自动调整列宽
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    for i, col in enumerate(summary_df.columns if sheet_name == '统计摘要' else df.columns):
                        # 找出列中最长的内容
                        max_len = max(
                            df[col].astype(str).map(len).max() if '融合目标详情' in sheet_name else summary_df[col].astype(str).map(len).max(),
                            len(str(col))
                        ) + 2  # 加2为了留出一些边距
                        # 设置列宽
                        worksheet.column_dimensions[chr(65 + i)].width = max_len

            print(f"统计结果已保存到Excel文件: {excel_path}")
            return excel_path
        else:
            print("没有统计数据可保存")
            return None

    def print_statistics_summary(self):
        """
        在终端打印统计结果摘要
        """
        print("\n" + "="*70)

        # 如果是筛选模式，在标题中标注
        if self.is_filtered and self.max_targets > 0:
            print(f"接力统计结果摘要 (基于中间{self.max_targets}个融合目标)")
        else:
            print("接力统计结果摘要")

        print("="*70)

        # 如果是筛选模式，添加筛选说明
        if self.is_filtered and self.max_targets > 0:
            print(f"注意: 本统计仅基于中间{self.max_targets}个融合目标及其相关的输入目标")
            print("-"*70)

        # 打印准确率
        accuracy = self.accuracy_results['accuracy'] * 100
        print(f"准确率: {accuracy:.2f}% ({self.accuracy_results['valid_targets']}/{self.accuracy_results['total_targets']})")
        print(f"  - 融合目标总数: {self.accuracy_results['total_targets']}")
        print(f"  - 有效融合目标数(首尾距离>{self.distance_threshold}m 且 经过≥{self.min_device_count}个设备): {self.accuracy_results['valid_targets']}")

        # 打印召回率
        recall = self.recall_results['recall'] * 100
        print(f"召回率: {recall:.2f}% ({self.recall_results['matched_targets']}/{self.recall_results['total_input_targets']})")
        print(f"  - 设备1且前进距离>{self.recall_distance_threshold}m的目标数: {self.recall_results['total_input_targets']}")
        print(f"  - 匹配成功的目标数: {self.recall_results['matched_targets']}")

        # 打印融合目标统计信息
        print("\n融合目标详细统计:")

        # 计算平均前进距离
        if self.target_details:
            avg_distance = sum(detail['total_distance'] for detail in self.target_details) / len(self.target_details)
            print(f"  - 平均首尾直线距离: {avg_distance:.2f}m")

            # 计算平均持续时间
            avg_duration = sum(detail['time_duration_ms'] for detail in self.target_details) / len(self.target_details)
            print(f"  - 平均持续时间: {avg_duration:.2f}ms")

            # 计算平均帧数
            avg_frames = sum(detail['frame_count'] for detail in self.target_details) / len(self.target_details)
            print(f"  - 平均帧数: {avg_frames:.2f}")

            # 统计设备数量分布
            device_count_stats = {}
            for detail in self.target_details:
                device_count = detail['device_count']
                device_count_stats[device_count] = device_count_stats.get(device_count, 0) + 1

            print(f"  - 设备数量分布:")
            for device_count in sorted(device_count_stats.keys()):
                count = device_count_stats[device_count]
                percentage = (count / len(self.target_details)) * 100
                print(f"    经过{device_count}个设备: {count}个目标 ({percentage:.1f}%)")

            # 计算平均设备数量
            avg_device_count = sum(detail['device_count'] for detail in self.target_details) / len(self.target_details)
            print(f"  - 平均经过设备数量: {avg_device_count:.2f}")

            # 统计真正的接力目标（经过≥指定数量个设备）
            relay_targets = sum(1 for detail in self.target_details if detail['device_count'] >= self.min_device_count)
            relay_percentage = (relay_targets / len(self.target_details)) * 100
            print(f"  - 真正的接力目标(≥{self.min_device_count}个设备): {relay_targets}个 ({relay_percentage:.1f}%)")

        print("="*70)
        print(f"详细统计结果已保存到Excel文件中，包含每个融合目标的详细信息。")
        print("="*70)

    def draw_statistics_panel(self, frame, x, y, width=300, height=120):
        """
        在图像上绘制统计结果面板

        参数:
            frame: 图像帧
            x: 面板左上角x坐标
            y: 面板左上角y坐标
            width: 面板宽度
            height: 面板高度
        """
        # 如果是筛选模式，增加面板高度以显示额外信息
        if self.is_filtered and self.max_targets > 0:
            height = 150

        # 创建半透明背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (x, y), (x + width, y + height), (240, 240, 240), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)

        # 绘制标题
        title = "接力统计"
        if self.is_filtered and self.max_targets > 0:
            title = f"接力统计 (中间{self.max_targets}个目标)"
        cv2.putText(frame, title, (x + 10, y + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)

        # 绘制准确率
        accuracy = self.accuracy_results['accuracy'] * 100
        accuracy_text = f"准确率: {accuracy:.2f}%"
        cv2.putText(frame, accuracy_text, (x + 10, y + 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # 绘制召回率
        recall = self.recall_results['recall'] * 100
        recall_text = f"召回率: {recall:.2f}%"
        cv2.putText(frame, recall_text, (x + 10, y + 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # 如果是筛选模式，添加筛选说明
        if self.is_filtered and self.max_targets > 0:
            filter_text = f"(筛选{self.max_targets}个融合目标)"
            cv2.putText(frame, filter_text, (x + 10, y + 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
