# 轨迹接力可视化系统 - 快速开始指南

## 🚀 5分钟快速上手

### 1. 环境准备
```bash
# 安装依赖
pip install opencv-python numpy pandas matplotlib openpyxl

# 检查Python版本（需要3.7+）
python --version
```

### 2. 基本使用

#### 方式一：使用真实日志数据
```bash
# 可视化模式（推荐新手）
python 轨迹接力可视化.py --log_file=你的日志文件.log --show_window=True

# 仅统计分析
python 轨迹接力可视化.py --log_file=你的日志文件.log --stats_only
```

#### 方式二：使用测试数据体验新功能
```bash
# 体验文本重叠修复功能
python test_text_overlap_fix.py --show_window=True
```

### 3. 🎮 键盘控制速查

| 按键 | 功能 | 说明 |
|------|------|------|
| **Space** | 暂停/播放 | 控制视频播放 |
| **A/D** | 后退/前进 | 跳转10帧 |
| **I** | 切换ID显示 | 目标旁边 ↔ 右上角面板 |
| **T** | 碰撞检测 | 开启/关闭文本重叠避免 |
| **B** | 文本背景 | 开启/关闭半透明背景 |
| **+/-** | 文本大小 | 增大/减小文本 |
| **C** | 显示模式 | 车辆图标 ↔ 圆点 |
| **ESC/Q** | 退出 | 关闭程序 |

### 4. ⭐ 新功能亮点演示

#### 文本重叠修复
当目标密度高时，系统会自动：
- 🎯 **智能布局**：寻找最佳文本位置
- 📏 **动态大小**：根据目标数量调整文本大小
- 🎨 **背景增强**：添加半透明背景提高可读性
- 🔍 **螺旋搜索**：在密集场景下寻找空位

#### 实时参数调整
- 按 `I` 键切换ID显示位置
- 按 `T` 键开启智能布局
- 按 `B` 键开启文本背景
- 按 `+/-` 键调整文本大小

### 5. 📊 统计分析功能

#### 快速统计
```bash
python 轨迹接力可视化.py --log_file=数据.log --stats_only --distance_threshold=800
```

#### 输出文件
- `fusion_targets_statistics.xlsx` - 详细目标统计
- `accuracy_recall_summary.xlsx` - 准确率召回率汇总

### 6. 🛠️ 常用参数配置

#### 高质量可视化
```bash
python 轨迹接力可视化.py \
  --log_file=数据.log \
  --scale_factor=3.0 \
  --display_range=2000 \
  --use_car_image=True \
  --show_window=True
```

#### 快速统计分析
```bash
python 轨迹接力可视化.py \
  --log_file=数据.log \
  --stats_only \
  --distance_threshold=800 \
  --max_targets=100 \
  --stats_output_dir=results
```

### 7. 🔧 故障排除

#### 问题：无法显示车辆图标
```bash
# 解决方案：使用圆点模式
python 轨迹接力可视化.py --log_file=数据.log --use_car_image=False
```

#### 问题：文本重叠严重
```bash
# 解决方案：启用智能布局（运行时按T键）
# 或者使用面板模式（按I键切换）
```

#### 问题：程序运行缓慢
```bash
# 解决方案：降低参数
python 轨迹接力可视化.py \
  --log_file=数据.log \
  --scale_factor=1.5 \
  --display_range=1000 \
  --max_targets=50
```

### 8. 📈 性能优化建议

#### 大数据集处理
- 使用 `--stats_only` 模式进行纯统计
- 设置 `--max_targets=100` 限制处理数量
- 降低 `--scale_factor` 和 `--display_range`

#### 实时显示优化
- 关闭文本背景（按B键）
- 使用圆点模式而非车辆图标
- 适当调整文本大小

### 9. 🎯 使用场景推荐

#### 场景1：日常数据分析
```bash
# 快速查看轨迹效果
python 轨迹接力可视化.py --log_file=daily_data.log
```

#### 场景2：算法性能评估
```bash
# 详细统计分析
python 轨迹接力可视化.py --log_file=test_data.log --stats_only --distance_threshold=800
```

#### 场景3：演示和展示
```bash
# 高质量可视化
python 轨迹接力可视化.py --log_file=demo_data.log --scale_factor=3.0 --use_car_image=True
```

#### 场景4：调试和优化
```bash
# 使用测试数据验证新功能
python test_text_overlap_fix.py --show_window=True
```

### 10. 📚 进阶使用

#### 自定义配置
```python
# 在代码中配置文本显示参数
visualizer.configure_text_display(
    collision_detection=True,
    dynamic_size=True,
    text_background=True,
    min_size=0.4,
    max_size=1.2
)
```

#### 批量处理
```bash
# 处理多个文件
for file in *.log; do
    python 轨迹接力可视化.py --log_file="$file" --stats_only
done
```

---

## 🆘 需要帮助？

1. **查看完整文档**：阅读 `README.md`
2. **查看技术细节**：阅读 `文本重叠修复方案说明.md`
3. **运行测试**：执行 `python test_text_overlap_fix.py`
4. **检查日志**：确认日志文件格式正确

## 🎉 开始使用

现在你已经掌握了基本使用方法，可以开始分析你的轨迹数据了！

记住：
- 🎮 多尝试键盘控制
- 📊 善用统计功能
- ⭐ 体验新的文本重叠修复功能
- 🔧 根据需要调整参数
