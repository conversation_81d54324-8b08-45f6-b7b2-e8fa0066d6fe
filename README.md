# 轨迹接力可视化系统 (TECU1000 Algorithm Show)

使用Python对日志文件进行动态可视化的轨迹接力分析工具

## 版本更新历史
- **5.13** 支持对日志文件进行解析，可视化物体的实时运动轨迹
- **5.16** 支持输入数据和输出数据的可视化
- **5.19** 拆分成两个窗口，支持轨迹接力算法可视化
- **5.20** 增加接力准确率统计功能 版本号：1.5.250520.1
- **⭐ 最新版本** 新增目标ID文本重叠修复功能，智能文本布局算法

## 主要功能特性

### 1. 轨迹数据可视化
- **双窗口显示**：左侧显示输入轨迹，右侧显示输出轨迹
- **实时播放控制**：支持暂停、快进、后退等操作
- **多种显示模式**：车辆图标或圆点标记
- **距离圆显示**：显示不同距离的参考圆圈
- **车道线绘制**：显示道路车道线信息

### 2. ⭐ 目标ID文本显示优化（新功能）
- **智能文本布局**：自动避免文本重叠，支持多候选位置策略
- **动态文本大小**：根据目标密度自动调整文本大小（0.4-1.2倍）
- **文本背景增强**：半透明背景和边框提高可读性
- **碰撞检测系统**：实时检测并避免文本重叠
- **螺旋搜索算法**：在密集场景下寻找最优文本位置
- **实时参数调整**：通过键盘快捷键实时调整显示参数

### 3. 接力统计分析
- **准确率计算**：统计目标前进距离超过阈值的比例
- **召回率计算**：统计成功匹配的输入目标比例
- **Excel报告生成**：自动生成详细的统计报告
- **实时统计面板**：在可视化界面中显示统计信息

## 接力准确率统计功能实现思路

### 1. 准确率统计
准确率统计功能用于评估接力目标的前进距离是否达到预期。具体实现如下：
- 定义前进距离阈值（默认800米）
- 按融合ID对目标进行分组，计算每个融合目标从首次出现到最后一次出现的直线距离
- 统计直线距离大于阈值的融合目标数量占总融合目标数量的比例
- 前进距离通过目标首次出现点和消失点之间的直线距离计算

### 2. 召回率统计
召回率统计功能用于评估输入目标是否都能产生对应的输出目标。具体实现如下：
- 遍历所有输入轨迹点，按设备ID和目标ID进行分组
- 遍历所有输出轨迹点，按设备ID和目标ID进行分组
- 统计有多少输入目标在输出中有对应的目标
- 计算匹配的目标数量占总输入目标数量的比例

### 3. 面向对象设计
- 创建`RelayStatistics`类封装统计功能
- 提供接口获取统计结果和在可视化界面上显示结果
- 与现有的可视化系统集成，在界面上实时显示统计信息

### 4. 统计结果保存与分析
- 支持将融合目标的详细统计结果保存到Excel文件中
- 统计内容包括：
  - 融合ID：目标的唯一标识
  - 组成的接力ID：参与接力的所有目标ID
  - 开始和结束时间：目标首次和最后一次出现的时间戳
  - 前进的总距离：目标从首次出现到最后一次出现的直线距离
  - 帧数：目标出现的总帧数
- Excel文件包含两个工作表：
  - "融合目标详情"：包含每个融合目标的详细信息
  - "统计摘要"：包含准确率、召回率、平均距离等汇总信息
- 提供统计模式，可以只进行统计而不显示可视化界面
- 在终端打印详细的统计结果摘要

### 5. 使用方法

程序提供两种运行模式：可视化模式和统计模式。

#### 可视化模式（默认）
在可视化模式下，程序会显示轨迹接力的可视化界面，支持实时交互控制。

```bash
# 基本可视化模式
python 轨迹接力可视化.py --log_file=日志文件路径

# 带参数的可视化模式
python 轨迹接力可视化.py --log_file=日志文件路径 --show_window=True --scale_factor=2.0 --display_range=1500.0
```

#### 统计模式
在统计模式下，程序不显示可视化界面，只进行统计分析，并将结果保存到Excel文件中。

```bash
# 仅统计模式
python 轨迹接力可视化.py --log_file=日志文件路径 --distance_threshold=800.0 --stats_only --stats_output_dir=统计结果输出目录
```

## 完整参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--log_file` | str | 默认路径 | 主日志文件路径 |
| `--log_lane_file` | str | lane.log | 车道线日志文件路径 |
| `--output_dir` | str | None | 输出目录 |
| `--output_name` | str | 治超检测可视化.avi | 输出视频文件名 |
| `--show_window` | bool | True | 是否显示实时窗口 |
| `--scale_factor` | float | 2.0 | 显示缩放因子 |
| `--display_range` | float | 1500.0 | 显示范围（米） |
| `--base_lon` | float | 112.989427 | 基准点经度 |
| `--base_lat` | float | 28.090128 | 基准点纬度 |
| `--circle_interval` | float | 100.0 | 距离圆间隔（米） |
| `--use_car_image` | bool | False | 是否使用车辆图片 |
| `--distance_threshold` | float | 100.0 | 准确率计算距离阈值（米） |
| `--recall_distance_threshold` | float | 100.0 | 召回率计算距离阈值（米） |
| `--stats_only` | flag | False | 仅进行统计分析 |
| `--stats_output_dir` | str | stats | 统计结果输出目录 |
| `--max_targets` | int | 100 | 统计的最大融合目标数量 |
| `--min_device_count` | int | 2 | 有效目标的最小设备数量 |

## ⭐ 交互式控制（新增功能）

在可视化窗口中，支持以下键盘控制：

### 基本控制
- **Space**：暂停/播放
- **A/D**：后退/前进10帧
- **ESC/Q**：退出程序

### 显示控制
- **C**：切换车辆图像/圆点显示
- **I**：切换ID显示模式（目标旁边 vs 右上角面板）

### ⭐ 文本显示优化控制（新增）
- **T**：开启/关闭文本碰撞检测
- **B**：开启/关闭文本背景
- **+/-**：增加/减少文本大小
- **R**：开启/关闭动态文本大小调整

## ⭐ 文本重叠修复功能详解

### 智能文本布局算法
系统采用多层次的文本布局策略：

1. **候选位置优先级**：
   - 右侧位置（优先）
   - 左侧位置
   - 上方位置
   - 下方位置

2. **螺旋搜索算法**：
   - 当所有候选位置被占用时启用
   - 从基础半径开始逐渐扩大搜索范围
   - 确保文本在帧边界内且不重叠

### 动态文本大小调整
根据当前帧中的目标数量自动调整文本大小：
- **≤5个目标**：使用最大文本大小（1.2）
- **6-15个目标**：使用标准文本大小（1.0）
- **16-25个目标**：使用较小文本大小（0.6）
- **>25个目标**：使用最小文本大小（0.4）

### 配置文本显示参数
可以通过代码配置文本显示参数：

```python
# 创建可视化器
visualizer = Visualizer(...)

# 配置文本显示参数
visualizer.configure_text_display(
    collision_detection=True,    # 启用碰撞检测
    dynamic_size=True,          # 启用动态大小调整
    text_background=True,       # 启用文本背景
    min_size=0.4,              # 最小文本大小
    max_size=1.2,              # 最大文本大小
    offset_radius=30,          # 文本偏移半径
    spacing=5                  # 文本间距
)

# 设置ID显示模式
visualizer.show_id_at_target = True  # 在目标旁边显示ID
```

## 测试和演示

### 运行测试脚本
```bash
# 使用测试数据演示文本重叠修复功能
python test_text_overlap_fix.py --show_window True

# 使用真实数据测试
python test_text_overlap_fix.py --log_file path/to/real/log.txt
```

测试脚本会创建高密度目标场景来演示文本重叠修复功能的效果。

## 输出文件

### 1. 可视化视频
- **格式**：AVI (XVID编码)
- **帧率**：10 FPS
- **内容**：左右分屏显示输入和输出轨迹

### 2. 统计报告
系统会在指定目录生成Excel文件：
- `fusion_targets_statistics.xlsx`：融合目标详细统计
- `accuracy_recall_summary.xlsx`：准确率和召回率汇总

## 故障排除

### 常见问题
1. **无法加载车辆图标**：确保 `assets/car.png` 文件存在
2. **日志文件解析错误**：检查日志文件格式和编码
3. **文本显示异常**：使用键盘控制调整文本参数
4. **内存不足**：减少目标数量限制或降低缩放因子

### 性能优化建议
- 大数据集使用 `--stats_only` 模式
- 关闭不必要的文本背景效果
- 设置合理的显示范围和缩放因子